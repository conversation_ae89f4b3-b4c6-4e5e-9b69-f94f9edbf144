'use client';

// import type { AxiosError } from 'axios';

import React from 'react';

import PinInput from 'react-pin-input';
import {
  Button,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  LoaderModal,
  // LinkButton,
  // LoaderModal,
  SuccessModal,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { usePlanForPlanOTP } from '../../api/payForPlan';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
// import { formatAxiosErrorMessage } from '@/utils/errors';


export interface WithdrawEntity {
  disburse_pin: string;
  payout_choice: string;
  amount: number;
  account_number: string;
  account_name: string;
  bank_code: string;
  bank_name: string;
}

export interface bankDetails {
  bankName: string;
  bankCode: string;
  bankAccount: string;
  bankAccountName: string;
  withdrawAmount: number;
  narration: string;
}

interface EnterWithdrawalPinModalProps {
  isPinModalOpen: boolean;
  setPinModalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  bankAmount: string;
}

export function EnterWithdrawalPinModal({
  isPinModalOpen,
  setPinModalState,
  heading,
  // bankAmount
}: EnterWithdrawalPinModalProps) {
  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { emailAddress, createPlanResponse, wallet_type } = useOnboardingPlan();



  const { mutate: payForPlan, isLoading: isPayForPlanLoading } =
    usePlanForPlanOTP(emailAddress as string);

  const handlePinSubmit = (value: string) => {
    payForPlan(
      {
        plan_id: Number(createPlanResponse?.id),
        wallet_type: String(wallet_type),
        otp: Number(value)
      },
      {
        onSuccess: () => {
          openSuccessModal()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );
  };

  if (isPayForPlanLoading) {
    return (
      <div>
        <LoaderModal />
      </div>
    );
  }

  return (
    <Dialog open={isPinModalOpen} onOpenChange={setPinModalState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="py-10 text-center">
          <form>
            <div className="flex flex-col justify-center bg-[#F5FAFF] py-4 px-8 rounded-lg">
              <p className="mx-auto mt-1 max-w-[262px] text-center text-[14px] text-[#6C727F] ">
                Dial USSD code shown below and enter OTP shown on your screen in the input below
              </p>

              <div className='mt-[14px] flex items-center gap-4 justify-center '>
                <h1 className='text-[#032282] text-xl font-extrabold'>*347*180*442#</h1>
                <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8.325 16.5625H5.175C3.77119 16.5625 2.86908 16.2488 2.31012 15.6899C1.75115 15.1309 1.4375 14.2288 1.4375 12.825V9.675C1.4375 8.27119 1.75115 7.36908 2.31012 6.81012C2.86908 6.25115 3.77119 5.9375 5.175 5.9375H8.325C9.72881 5.9375 10.6309 6.25115 11.1899 6.81012C11.7488 7.36908 12.0625 8.27119 12.0625 9.675V12.825C12.0625 14.2288 11.7488 15.1309 11.1899 15.6899C10.6309 16.2488 9.72881 16.5625 8.325 16.5625ZM5.175 6.0625C3.98295 6.0625 3.02841 6.25824 2.39332 6.89332C1.75824 7.52841 1.5625 8.48295 1.5625 9.675V12.825C1.5625 14.017 1.75824 14.9716 2.39332 15.6067C3.02841 16.2418 3.98295 16.4375 5.175 16.4375H8.325C9.51705 16.4375 10.4716 16.2418 11.1067 15.6067C11.7418 14.9716 11.9375 14.017 11.9375 12.825V9.675C11.9375 8.48295 11.7418 7.52841 11.1067 6.89332C10.4716 6.25824 9.51705 6.0625 8.325 6.0625H5.175Z" fill="#032282" stroke="#032282" />
                  <path d="M12.0625 11.4375V11.9375H12.5625H12.825C14.017 11.9375 14.9716 11.7418 15.6067 11.1067C16.2418 10.4716 16.4375 9.51705 16.4375 8.325V5.175C16.4375 3.98295 16.2418 3.02841 15.6067 2.39332C14.9716 1.75824 14.017 1.5625 12.825 1.5625H9.675C8.48295 1.5625 7.52841 1.75824 6.89332 2.39332C6.25824 3.02841 6.0625 3.98295 6.0625 5.175V5.4375V5.9375H6.5625H8.325C9.72881 5.9375 10.6309 6.25115 11.1899 6.81012C11.7488 7.36908 12.0625 8.27119 12.0625 9.675V11.4375ZM12.825 12.0625H12C11.9869 12.0625 11.9713 12.0573 11.957 12.043C11.9427 12.0287 11.9375 12.0131 11.9375 12V9.675C11.9375 8.48295 11.7418 7.52841 11.1067 6.89332C10.4716 6.25824 9.51705 6.0625 8.325 6.0625H6C5.98694 6.0625 5.97128 6.0573 5.95699 6.04301C5.9427 6.02872 5.9375 6.01306 5.9375 6V5.175C5.9375 3.77119 6.25115 2.86908 6.81012 2.31012C7.36908 1.75115 8.27119 1.4375 9.675 1.4375H12.825C14.2288 1.4375 15.1309 1.75115 15.6899 2.31012C16.2488 2.86908 16.5625 3.77119 16.5625 5.175V8.325C16.5625 9.72881 16.2488 10.6309 15.6899 11.1899C15.1309 11.7488 14.2288 12.0625 12.825 12.0625Z" fill="#032282" stroke="#032282" />
                </svg>

              </div>
            </div>
            <div className="pin-input-container mx-auto mt-[27px] flex w-full justify-between gap-4 text-xl">
              <PinInput
                autoSelect={false}
                initialValue="o"
                inputFocusStyle={{ borderColor: '#4C1961' }}
                inputMode="number"
                inputStyle={{
                  marginRight: '10px',
                  background: '#F5F7F9',
                  borderRadius: '14px',
                  border: '#ffffff',
                  fontSize: '14px',
                }}
                length={4}
                style={{ padding: '10px', margin: 'auto' }}
                type="numeric"
                onComplete={handlePinSubmit}
              />
            </div>
          </form>
        </DialogBody>
      </DialogContent>

      {/* <SuccessPayrollModal
        funText='"Enjoyyyyy!!!! 🙌🙌"'
        heading="Withdrawal Successful"
        isSuccessPayrollModalOpen={isSuccessModalOpen}
        setSuccessPayrollModalState={setSuccessModalState}
        subheading={`You have successfully withdrawn the sum of ₦${employeeBankDetails.withdrawAmount} from your wallet`}
      >
        <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
          <Button
            className="grow text-base"
            size="lg"
            variant="default"
            onClick={() => {
              location.reload();
            }}
          >
            Done
          </Button>
        </div>
      </SuccessPayrollModal> */}

      <SuccessModal
        heading="Successful"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState} />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 text-base"
            size="lg"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </Dialog>
  );
}
