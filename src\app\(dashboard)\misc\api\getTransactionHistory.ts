import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface TransactionHistoryData {
  status: boolean
  count: number
  next: string
  previous: string
  results: TransactionHistoryEntity[]
}

export interface TransactionHistoryEntity {
  id: number
  status: string
  amount: number
  date_created: string
  transaction_id: string
  description: string
  transaction_form_type: string
  transaction_date_completed: string
  transaction_type: string
  wallet_type: string
  wallet_balance_before: number
  wallet_balance_after: number
}



export const getTransactionHistory = async (email: string,_phone_number?: string ): Promise<TransactionHistoryData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get(`/onlending/transaction-history`, {headers});
  return data;
};

export const useTransactionHistory = (email: string, phone_number?: string) =>
  useQuery('on-lending-TransactionHistory', () => getTransactionHistory(email, phone_number) );
 