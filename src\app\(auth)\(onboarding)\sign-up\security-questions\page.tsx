import * as React from 'react';

import { OnboardingPageWrapper } from '../../misc/components';
import { SecurityQuestionsForm } from './misc/components';

export default async function SecurityQuestions({
  searchParams,
}: {
  searchParams: { phone: string };
}) {
  const { phone } = searchParams;


  return (
    <>
      <div className="mx-auto max-w-[32.375rem] px-2 md:hidden">
        <p className="relative mb-2 ml-auto w-max text-white md:hidden">4/7</p>
      </div>

      <OnboardingPageWrapper
        heading="Security Questions"
        subHeading="This questions would serve as an extra security measures for your account."
      >
        <SecurityQuestionsForm
          phone={phone}
        />
      </OnboardingPageWrapper>
    </>
  );
}
