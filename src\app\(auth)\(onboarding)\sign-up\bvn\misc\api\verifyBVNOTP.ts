import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface VerifyBvnOtpDto {
  phone_number: string;
  otp: string;
  app_name: string;
}

export interface VerifyBVNOTPResponse {
  message: string;
}

// const verifyBVNOTP = async (verifyBVNDTO: VerifyBvnOtpDto) => {
//   const response = await adminLoanAxios.post(
//     '/kyc/user/confirm_bvn_otp/',
//     verifyBVNDTO
//   );
//   return response.data as VerifyBVNOTPResponse;
// };

// export const useVerifyBVNOTP = () => {
//   return useMutation({
//     mutationFn: verifyBVNOTP,
//   });
// };

const verifyBVNOTP = (verifyBVNDTO: VerifyBvnOtpDto): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/confirm_registration_email/`, verifyBVNDTO);
};

export const useVerifyBVNOTP = () => {
  return useMutation('verifyBvnOTP', verifyBVNOTP, {});
};