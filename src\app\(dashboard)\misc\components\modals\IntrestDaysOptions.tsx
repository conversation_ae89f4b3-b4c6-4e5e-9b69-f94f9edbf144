'use client';

import React from 'react';
import { z } from 'zod';

import {
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/core';
import { useOnboardingPlan } from '@/store/onBoardingPlan';



interface IntrestDaysOptionsModalProps {
  isIntrestDaysOptionsOpen: boolean;
  setIntrestDaysOptionsState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
}

const IntrestDaysOptionsSchema = z.object({
  IntrestDaysOptions: z
    .string({ required_error: 'Please select a wallet type.' })
    .trim()
    .min(1, { message: 'Please select a wallet type.' }),
});

export type IntrestDaysOptionsValues = z.infer<typeof IntrestDaysOptionsSchema>;

export function IntrestDaysOptions({
  isIntrestDaysOptionsOpen,
  setIntrestDaysOptionsState,
  heading,
}: IntrestDaysOptionsModalProps) {

  const { intrest_day_response, setDuration } = useOnboardingPlan();
  return (
    <Dialog open={isIntrestDaysOptionsOpen} onOpenChange={setIntrestDaysOptionsState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="text-left">
          <div className='mt-3'>
            <div className="radio-group space-y-2">
              {intrest_day_response?.data.map(({ days, rate }, index) => {
                return (
                  <div key={index} onClick={() => {
                    setDuration(days)
                    setIntrestDaysOptionsState(false)
                  }
                  }
                  >
                    <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor={`${index}`}>
                      <div className="flex gap-[14px] items-center flex-row-reverse">
                        <input
                          className="focus:outline-none"
                          id={`${index}`}
                          type="radio"
                          value={`${index}`}
                        />
                        <p className='text-[#032282] text-xs font-medium'>{rate}% pa </p>
                      </div>
                      <p className='text-[#4E4E4E] text-xs font-semibold'>{`${days} days loans`}</p>
                    </label>
                  </div>
                )
              })}
            </div>
          </div>
        </DialogBody>
      </DialogContent>

    </Dialog>
  );
}
