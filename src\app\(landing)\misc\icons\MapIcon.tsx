import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg
        fill="none"
        height={24}
        viewBox="0 0 24 24"
        width={24}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M19.8801 20.94C18.9301 21.64 17.6801 22 16.1901 22H7.81009C7.57009 22 7.3301 21.99 7.1001 21.96L14.0001 15.06L19.8801 20.94Z"
            fill="#1255C3"
            opacity={0.4}
        />
        <path
            d="M22.0001 7.80997V16.19C22.0001 17.68 21.6401 18.93 20.9401 19.88L15.0601 14L21.9601 7.09998C21.9901 7.32998 22.0001 7.56997 22.0001 7.80997Z"
            fill="#1255C3"
            opacity={0.4}
        />
        <path
            d="M15.06 14L20.94 19.88C20.65 20.3 20.3 20.65 19.88 20.94L14 15.06L7.10001 21.96C6.46001 21.92 5.88001 21.79 5.35001 21.59C3.21001 20.81 2 18.91 2 16.19V7.81C2 4.17 4.17 2 7.81 2H16.19C18.91 2 20.81 3.21 21.59 5.35C21.79 5.88 21.92 6.46 21.96 7.1L15.06 14Z"
            fill="#1255C3"
            opacity={0.4}
        />
        <path
            d="M15.0601 14L20.9401 19.88C20.6501 20.3 20.3001 20.65 19.8801 20.94L14.0001 15.06L7.1001 21.96C6.4601 21.92 5.8801 21.79 5.3501 21.59L5.74008 21.2L21.5901 5.34998C21.7901 5.87998 21.9201 6.45998 21.9601 7.09998L15.0601 14Z"
            fill="#1255C3"
        />
        <path
            d="M12.2398 7.93003C11.8598 6.28003 10.3998 5.54003 9.11981 5.53003C7.83981 5.53003 6.37981 6.27003 5.99981 7.92003C5.57981 9.75003 6.6998 11.28 7.7098 12.24C8.1098 12.62 8.60981 12.8 9.11981 12.8C9.62981 12.8 10.1298 12.61 10.5298 12.24C11.5398 11.28 12.6598 9.75003 12.2398 7.93003ZM9.14981 9.49003C8.59981 9.49003 8.14981 9.04003 8.14981 8.49003C8.14981 7.94003 8.58981 7.49003 9.14981 7.49003H9.15982C9.70982 7.49003 10.1598 7.94003 10.1598 8.49003C10.1598 9.04003 9.69981 9.49003 9.14981 9.49003Z"
            fill="#1255C3"
        />
    </svg>
);
export default SVGComponent;
