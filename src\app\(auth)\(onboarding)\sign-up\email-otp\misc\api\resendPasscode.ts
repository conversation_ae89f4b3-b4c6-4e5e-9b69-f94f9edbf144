import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';

export interface ResendPasscodeDTO {
  email: string;
}

export interface ResendPasscodeResponse {
  message: string;
}

  const resendPasscode = (resendPasscodeDTO: ResendPasscodeDTO): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/resend_registration_pin/`, resendPasscodeDTO);
};

export const useResendPasscode = () => {
  return useMutation('resendEmailOTP', resendPasscode, {});
};