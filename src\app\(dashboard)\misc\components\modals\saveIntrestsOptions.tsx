'use client';

import React from 'react';
import { z } from 'zod';

import {
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { EnterWithdrawalPinModal } from './EnterPaymentPinModal';
import { convertNumberToNaira } from '@/utils/currency';
import { useOverview } from '../../api/overview';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { EnterAgentsWithdrawalModal } from './EnterAgentsPaymentPinModal';



interface SaveIntrestsOptionsModalProps {
  isSaveIntrestsOptionsOpen: boolean;
  setSaveIntrestsOptionsState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  email: string;
}

const SaveIntrestsOptionsSchema = z.object({
  SaveIntrestsOptions: z
    .string({ required_error: 'Please select a wallet type.' })
    .trim()
    .min(1, { message: 'Please select a wallet type.' }),
});

export type SaveIntrestsOptionsValues = z.infer<typeof SaveIntrestsOptionsSchema>;

export function SaveIntrestsOptions({
  isSaveIntrestsOptionsOpen,
  setSaveIntrestsOptionsState,
  heading,
  email
}: SaveIntrestsOptionsModalProps) {

  const {
    state: isSaveIntrestOptionsOpen,
    setState: setSaveIntrestOptionsState,
    setTrue: openSaveIntrestOptions,
  } = useBooleanStateControl();

  const {
    state: isAgentsTransactionPinModalOpen,
    setState: setAgentsTransactionPinModalState,
    setTrue: openAgentsTransactionPinModal,
  } = useBooleanStateControl();



  const { data: user_overview } = useOverview(email as string);

  const { setWalletType } = useOnboardingPlan();



  return (
    <Dialog open={isSaveIntrestsOptionsOpen} onOpenChange={setSaveIntrestsOptionsState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="text-left">
          <p className='text-xs font-semibold'>Select wallet you want your savings amount to me
            deducted from</p>

          <div className='space-y-2'>

            <div className='cursor-pointer flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]' onClick={() => {
              setWalletType('ONLENDING_MAIN')
              openAgentsTransactionPinModal()
            }}>
              <div className='flex gap-[10px] items-center'>
                <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                  <rect fill="#032282" height="32" rx="7" width="32" />
                  <path d="M23.5163 21.0707V23.1345H9.41058V21.0707C9.41058 20.6924 9.72796 20.3828 10.1159 20.3828H22.8111C23.199 20.3828 23.5163 20.6924 23.5163 21.0707Z" fill="white" stroke="white" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                  <path d="M13.6423 15.5674H10.8212V20.3828H13.6423V15.5674Z" fill="white" opacity="0.6" />
                  <path d="M16.4635 15.5674H13.6423V20.3828H16.4635V15.5674Z" fill="white" opacity="0.4" />
                  <path d="M19.2846 15.5674H16.4634V20.3828H19.2846V15.5674Z" fill="white" opacity="0.6" />
                  <path d="M22.1058 15.5674H19.2846V20.3828H22.1058V15.5674Z" fill="white" opacity="0.4" />
                  <path d="M24.2216 23.65H8.7053C8.41613 23.65 8.17633 23.4161 8.17633 23.1341C8.17633 22.8521 8.41613 22.6182 8.7053 22.6182H24.2216C24.5108 22.6182 24.7506 22.8521 24.7506 23.1341C24.7506 23.4161 24.5108 23.65 24.2216 23.65Z" fill="white" />
                  <path d="M23.072 11.9553L16.7244 9.47878C16.5834 9.42374 16.3436 9.42374 16.2025 9.47878L9.85492 11.9553C9.60807 12.0516 9.41058 12.3336 9.41058 12.595V14.8789C9.41058 15.2573 9.72796 15.5668 10.1159 15.5668H22.8111C23.199 15.5668 23.5163 15.2573 23.5163 14.8789V12.595C23.5163 12.3336 23.3189 12.0516 23.072 11.9553ZM16.4635 13.847C15.8781 13.847 15.4055 13.3861 15.4055 12.8152C15.4055 12.2442 15.8781 11.7833 16.4635 11.7833C17.0489 11.7833 17.5214 12.2442 17.5214 12.8152C17.5214 13.3861 17.0489 13.847 16.4635 13.847Z" fill="white" />
                </svg>

                <div>
                  <p className='text-[#242424] text-sm font-semibold'>Fund from main balance</p>
                  <p className='text-xs font-medium text-[#646464]'>Bal: {`₦${convertNumberToNaira(Number(user_overview?.data?.total_balance), false)}`}</p>
                </div>
              </div>
            </div>

            <div className='cursor-pointer flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]'
              onClick={() => {
                setWalletType('AJO_SPENDING')
                openSaveIntrestOptions()
              }}>
              <div className='flex gap-[10px] items-center'>
                <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                  <rect fill="#032282" height="32" rx="7" width="32" />
                  <path d="M19.75 22.375H12.25C10 22.375 8.5 21.25 8.5 18.625V13.375C8.5 10.75 10 9.625 12.25 9.625H19.75C22 9.625 23.5 10.75 23.5 13.375V18.625C23.5 21.25 22 22.375 19.75 22.375Z" fill="white" />
                  <path d="M16 18.25C17.2426 18.25 18.25 17.2426 18.25 16C18.25 14.7574 17.2426 13.75 16 13.75C14.7574 13.75 13.75 14.7574 13.75 16C13.75 17.2426 14.7574 18.25 16 18.25Z" fill="#032282" />
                  <path d="M11.125 18.4375C10.8175 18.4375 10.5625 18.1825 10.5625 17.875V14.125C10.5625 13.8175 10.8175 13.5625 11.125 13.5625C11.4325 13.5625 11.6875 13.8175 11.6875 14.125V17.875C11.6875 18.1825 11.4325 18.4375 11.125 18.4375Z" fill="#032282" />
                  <path d="M20.875 18.4375C20.5675 18.4375 20.3125 18.1825 20.3125 17.875V14.125C20.3125 13.8175 20.5675 13.5625 20.875 13.5625C21.1825 13.5625 21.4375 13.8175 21.4375 14.125V17.875C21.4375 18.1825 21.1825 18.4375 20.875 18.4375Z" fill="#032282" />
                </svg>


                <div>
                  <p className='text-[#242424] text-sm font-semibold'>Save from spend wallet</p>
                  <p className='text-xs font-medium text-[#646464]'>New Ride savings. Bal:<span className='font-semibold'>₦400,000</span>  </p>
                </div>
              </div>
            </div>

            {/* <div className='cursor-pointer flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]' onClick={openSaveIntrestOptions}>
              <div className='flex gap-[10px] items-center'>
                <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                  <rect fill="#032282" height="32" rx="7" width="32" />
                  <path d="M19.75 22.375H12.25C10 22.375 8.5 21.25 8.5 18.625V13.375C8.5 10.75 10 9.625 12.25 9.625H19.75C22 9.625 23.5 10.75 23.5 13.375V18.625C23.5 21.25 22 22.375 19.75 22.375Z" fill="white" />
                  <path d="M16 18.25C17.2426 18.25 18.25 17.2426 18.25 16C18.25 14.7574 17.2426 13.75 16 13.75C14.7574 13.75 13.75 14.7574 13.75 16C13.75 17.2426 14.7574 18.25 16 18.25Z" fill="#032282" />
                  <path d="M11.125 18.4375C10.8175 18.4375 10.5625 18.1825 10.5625 17.875V14.125C10.5625 13.8175 10.8175 13.5625 11.125 13.5625C11.4325 13.5625 11.6875 13.8175 11.6875 14.125V17.875C11.6875 18.1825 11.4325 18.4375 11.125 18.4375Z" fill="#032282" />
                  <path d="M20.875 18.4375C20.5675 18.4375 20.3125 18.1825 20.3125 17.875V14.125C20.3125 13.8175 20.5675 13.5625 20.875 13.5625C21.1825 13.5625 21.4375 13.8175 21.4375 14.125V17.875C21.4375 18.1825 21.1825 18.4375 20.875 18.4375Z" fill="#032282" />
                </svg>


                <div>
                  <p className='text-[#242424] text-sm font-semibold'>Save from spend wallet</p>
                  <p className='text-xs font-medium text-[#646464]'>Shop rent savings. Bal: <span className='font-semibold'>₦400,000</span>  </p>
                </div>
              </div>
            </div> */}

          </div>
        </DialogBody>
      </DialogContent>

      <EnterAgentsWithdrawalModal
        bankAmount={''}
        heading='Transaction Pin'
        isAgentPinModalOpen={isAgentsTransactionPinModalOpen}
        setAgentPinModalState={setAgentsTransactionPinModalState} />


      <EnterWithdrawalPinModal
        bankAmount={''}
        heading='Enter USSD'
        isPinModalOpen={isSaveIntrestOptionsOpen}
        setPinModalState={setSaveIntrestOptionsState} />


    </Dialog>
  );
}
