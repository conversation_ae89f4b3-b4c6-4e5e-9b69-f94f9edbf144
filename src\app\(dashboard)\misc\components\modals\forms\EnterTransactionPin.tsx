'use client';

// import type { AxiosError } from 'axios';

import React from 'react';

import PinInput from 'react-pin-input';
import {
  Button,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  LoaderModal,
  // LinkButton,
  // LoaderModal,
  SuccessModal,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { useCreateAccount } from '../../../api/withdrawals/createAccount';
// import { formatAxiosErrorMessage } from '@/utils/errors';


export interface WithdrawEntity {
  disburse_pin: string;
  payout_choice: string;
  amount: number;
  account_number: string;
  account_name: string;
  bank_code: string;
  bank_name: string;
}

export interface bankDetails {
  bankName: string;
  bankCode: string;
  bankAccount: string;
  bankAccountName: string;
  withdrawAmount: number;
}

interface EnterWithdrawalPinModalProps {
  isPinModalOpen: boolean;
  setPinModalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  withdrawalDetails: bankDetails;
}

export function EnterWithdrawalDetailsPinModal({
  isPinModalOpen,
  setPinModalState,
  heading,
  withdrawalDetails
}: EnterWithdrawalPinModalProps) {

  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { emailAddress } = useOnboardingPlan();



  const { mutate: postCreateAccount, isLoading: isPostCreateAccountLoading } =
    useCreateAccount(emailAddress as string);

  const handlePinSubmit = (value: string) => {
    postCreateAccount({
      account_number: withdrawalDetails.bankAccount,
      bank_name: withdrawalDetails.bankName,
      bank_code: withdrawalDetails.bankCode,
      account_name: withdrawalDetails.bankAccountName,
      transaction_pin: value
    },
      {
        onSuccess: () => {
          openSuccessModal()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    )
  };

  if (isPostCreateAccountLoading) {
    return (
      <div>
        <LoaderModal />
      </div>
    );
  }

  return (
    <Dialog open={isPinModalOpen} onOpenChange={setPinModalState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="py-10 text-center">
          <form>
            <div className="flex flex-col justify-center bg-[#F5FAFF] py-4 px-8 rounded-lg">
              <p className="mx-auto mt-1 max-w-[262px] text-center text-[14px] text-[#6C727F] ">
                Dial USSD code shown below and enter OTP shown on your screen in the input below
              </p>

            </div>
            <div className="pin-input-container mx-auto mt-[27px] flex w-full justify-between gap-4 text-xl">
              <PinInput
                autoSelect={false}
                initialValue="o"
                inputFocusStyle={{ borderColor: '#4C1961' }}
                inputMode="number"
                inputStyle={{
                  marginRight: '10px',
                  background: '#F5F7F9',
                  borderRadius: '14px',
                  border: '#ffffff',
                  fontSize: '14px',
                }}
                length={4}
                style={{ padding: '10px', margin: 'auto' }}
                type="numeric"
                onComplete={handlePinSubmit}
              />
            </div>
          </form>
        </DialogBody>
      </DialogContent>


      <SuccessModal
        heading="Successful"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState} />

      <SuccessModal
        heading="Successful"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState} />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 text-base"
            size="lg"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </Dialog>
  );
}
