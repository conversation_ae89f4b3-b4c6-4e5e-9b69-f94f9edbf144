import { cva, VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/utils/classNames';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-lg text-center text-xs font-medium ring-offset-white transition duration-300 hover:opacity-75 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 active:scale-90 disabled:pointer-events-none disabled:scale-100 disabled:opacity-40 disabled:hover:cursor-not-allowed',
  {
    variants: {
      variant: {
        default: 'bg-[#1255C3] text-white',
        secondary: 'bg-[#081934] text-white',
        light: 'bg-main-bg text-main-solid',
        white: 'bg-white text-main-solid',
        red: 'bg-red-800 text-red-100',
        outlined: 'border border-main-solid bg-transparent text-main-solid',
        unstyled: '',
      },
      size: {
        default: 'px-6 py-2',
        lg: 'rounded-lg px-6 py-3.5',
        fullWidth: 'block w-full py-2',
        fullWidthAlign: 'flex items-center justify-start w-full py-2',
        unstyled: '',
        icon: "h-8 w-8"
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> { }

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
