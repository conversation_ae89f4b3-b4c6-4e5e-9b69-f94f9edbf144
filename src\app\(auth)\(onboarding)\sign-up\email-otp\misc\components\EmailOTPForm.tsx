/* eslint-disable @typescript-eslint/ban-ts-comment */
'use client';

import * as React from 'react';

import type { AxiosError } from 'axios';
// import { useRouter } from 'next/navigation';
import PinInput from 'react-pin-input';

import { Button } from '@/components/core/Button';
import { ErrorModal } from '@/components/core/ErrorModal';
import { LoaderModal } from '@/components/core/LoaderModal';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { launchNotification } from '@/utils/notifications';

import { useResendPasscode, useVerifyEmailOTP } from '../api';
import { LendingOnboardingOptions } from '@/app/(dashboard)/misc/components/modals/LendingOnboardingOptions';

interface EmailOTPFormProps {
  email: string;
  phone: string;
}

export function EmailOTPForm({ email, phone }: EmailOTPFormProps) {
  const [passcode, setPasscode] = React.useState('');

  const [onBoardingOptionDetails, setOnboardingOptionDetails] = React.useState({
    phoneNumber: phone,
    emailDetail: email
  })

  const {
    state: isLendingOptionOpen,
    setState: setLendingOptionState,
    setTrue: openLendingOption,
  } = useBooleanStateControl();


  // const router = useRouter();

  // const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
  //   useBooleanStateControl();



  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { mutate: verifyEmailOTP, isLoading: isVerifyEmailOTPLoading } =
    useVerifyEmailOTP();

  const handleOTPValidation = (email: string, passcode: string) => {
    verifyEmailOTP(
      { email, passcode },
      {
        onSuccess: () => {


          setOnboardingOptionDetails({
            phoneNumber: phone,
            emailDetail: email
          })

          // openLoaderModal();

          // router.push(
          //   `/sign-up/next-of-kin/?email=${email}&phone=${phone}`
          // );
          openLendingOption()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };

  const handleComplete = (passcode: string) => {
    setPasscode(passcode);
    handleOTPValidation(email, passcode);
    // router.push(
    //   `/sign-up/bvn/?email=${email}&phone=${phone}`
    // );
  };

  const handleContinueClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault;
    handleOTPValidation(email, passcode);
  };

  const { mutate: resendPasscode, isLoading: isResendPasscodeLoading } =
    useResendPasscode();

  const handleResendPasscode = () => {
    resendPasscode(
      { email },
      {
        onSuccess: () => {
          launchNotification('success', 'New passcode sent');
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };

  return (
    <>
      <LoaderModal
        isOpen={
          isVerifyEmailOTPLoading ||
          isResendPasscodeLoading
        }
      />

      <form className="relative">
        <PinInput
          autoSelect={false}
          initialValue="o"
          inputFocusStyle={{
            border: '3px solid #403C3A',
            borderRadius: '.625rem',
            padding: '0.5rem',
            outline: 'none',
            color: 'white',
            background: 'rgba(255, 255, 255, 0.3)',
            boxShadow: '0 0 0 1px rgb(255, 255, 255)',
          }}
          inputMode="number"
          inputStyle={{
            marginRight: '.3125rem',
            marginLeft: '.3125rem',
            background: '#F5F7F9',
            borderRadius: '14px',
            fontSize: '15px',
            fontWeight: '500',
            width: 'calc(16.5% - 10px)',
            height: 'unset',
            aspectRatio: '1',
            transitionDuration: '300ms',
            transitionProperty:
              'color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter',
            transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          length={6}
          style={{ maxWidth: '25rem' }}
          type="numeric"
          onComplete={handleComplete}
        />

        <Button
          className="mx-auto mt-4 flex w-max gap-2 text-xxs font-normal text-white"
          size="unstyled"
          type="button"
          variant="unstyled"
          onClick={handleResendPasscode}
        >
          <svg
            fill="none"
            height={24}
            viewBox="0 0 24 24"
            width={24}
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx={12} cy={12} fill="#F2F6FF" r={12} />
            <path
              d="M5.75 12.606V8.87a3.12 3.12 0 0 1 3.125-3.12h6.25a3.12 3.12 0 0 1 3.125 3.119v4.362a3.12 3.12 0 0 1-3.125 3.113h-.938a.633.633 0 0 0-.5.25l-.937 1.243c-.412.55-1.088.55-1.5 0l-.938-1.243a.695.695 0 0 0-.5-.25h-.937A3.12 3.12 0 0 1 5.75 13.23v-.625Z"
              fill="#073D9F"
              opacity={0.6}
            />
            <path
              d="M12 12a.623.623 0 0 1-.625-.625c0-.344.281-.625.625-.625s.625.281.625.625A.623.623 0 0 1 12 12Zm2.5 0a.623.623 0 0 1-.625-.625c0-.344.281-.625.625-.625s.625.281.625.625A.623.623 0 0 1 14.5 12Zm-5 0a.623.623 0 0 1-.625-.625c0-.344.281-.625.625-.625s.625.281.625.625A.623.623 0 0 1 9.5 12Z"
              fill="#fff"
            />
          </svg>

          <span>Resend passcode</span>
        </Button>

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isVerifyEmailOTPLoading}
          type="button"
          variant="white"
          onClick={handleContinueClick}
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isVerifyEmailOTPLoading ? 'Loading' : 'Continue'}</span>
            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
      </form>

      <LendingOnboardingOptions
        heading={'Lending options'}
        isLendingOnboardingOptionsOpen={isLendingOptionOpen}
        setLendingOnboardingOptionsState={setLendingOptionState}
        onBoardingOptionDetails={onBoardingOptionDetails}
      />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
