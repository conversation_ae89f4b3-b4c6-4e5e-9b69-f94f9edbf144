import { create, StateCreator } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';


export interface OnBoardingEntity {
  plan_name?: string
  amount?: string
  intrest_due?: string
  intrest_text?: string
  paymentOption?: string
  duration?: number
}

export interface CrratePlanResponse {
  status: boolean
  data: CreatePlanResponseEntity
}

export interface CreatePlanResponseEntity {
  id: number
  name: string
  duration: number
  target: number
  quotation_id: string
  maturity_date: string
  interest_type: string
}

export interface IntrestDaysData {
  status: boolean
  data: IntrestDaysEntity[]
}

export interface IntrestDaysEntity {
  days: number
  rate: number
}

interface OnboardingPlanDataSlice {
  // ONBOARDING 
  referall_code_onboarding: string | null;
  token_onboarding: string | null;
  interest_type_onboarding: string | null;
  interest_type_text_onboarding: string | null;
  plan_name_onboarding: string | null;
  target_onboarding: string | null;
  duration_onboarding: number | null;
  setTokenOnboarding: (newTokenOnboarding: string) => void;
  setReferallCodeOnboading: (newReferallCodeOnboading: string | null) => void;
  setIntrestTypeOnboarding: (newIntrestTypeOnboarding: string) => void;
  setPlanNameOnboarding: (newPlanNameOnboarding: string) => void;
  setTargetOnboarding: (newTargetOnboarding: string) => void;
  setDurationOnboarding: (newDurationOnboarding: number) => void;
  setIntrestTypeTextOnboarding: (newIntrestTypeTextOnboarding: string) => void;
  
  phone_number: string | null;
  boardingPlanData: OnBoardingEntity | null;
  createPlanResponse: CreatePlanResponseEntity | null;
  emailAddress: string | null;
  plan_detail_id: number | null;
  plan_type: string | null;
  planName: string | null;
  target: string | null;
  duration: number | null;
  interest_type: string | null;
  wallet_type: string | null;
  intrest_day_response: IntrestDaysData | null;
  setBoardingPlanData: (newTransactionHistoryData: OnBoardingEntity) => void;
  setEmailAddress: (newEmailAddress: string) => void;
  setPhoneNumber: (newPhoneNumber: string) => void;
  setDuration: (newDuration: number) => void;
  setPlanDetailsId: (newPlanDetailsId: number) => void;
  setIntrestType: (newIntrestType: string) => void;
  setWalletType: (newWalletType: string) => void;
  setPlanName: (newPlanName: string) => void;
  setTarget: (newTarget: string) => void;
  setPlanType: (newPlanType: string) => void;
  setCreatePlanResponse: (newCreatePlanResponse: CreatePlanResponseEntity) => void;
  setIntrestDaysResponse: (newIntrestDaysResponse: IntrestDaysData) => void;
  clear: () => void; // New clear action
}

const createOnboardingPlanDataSlice: StateCreator<
  OnboardingPlanDataSlice,
  [['zustand/persist', unknown]],
  [],
  OnboardingPlanDataSlice
> = set => ({
  boardingPlanData: null,
  createPlanResponse: null,
  emailAddress: null,
  duration: null,
  interest_type: null,
  planName:null,
  target: null,
  wallet_type: null,
  intrest_day_response:null,
  plan_type: null,
  plan_detail_id: null,
  phone_number: null,
  token_onboarding: null,
  referall_code_onboarding: null,

    // ONBOARDING 
    interest_type_onboarding: null,
    plan_name_onboarding: null,
    target_onboarding: null,
    duration_onboarding: null,
    interest_type_text_onboarding: null,
  
// CLEAR ON CALL
  clear: () =>
  set(() => ({
    boardingPlanData: null,
    emailAddress: null,
    interest_type: null,
    planName: null,
    duration: null,
    target: null,
    createPlanResponse: null,
    wallet_type: null,
    intrest_day_response: null,
    plan_type: null,
    plan_detail_id: null,
    phone_number: null,
    token_onboarding: null,
    referall_code_onboarding: null,

       // ONBOARDING 
       interest_type_onboarding: null,
       plan_name_onboarding: null,
       target_onboarding: null,
       duration_onboarding: null,
       interest_type_text_onboarding: null,
  })),
  
setBoardingPlanData: sign_up_data =>
  set(() => ({
    boardingPlanData: sign_up_data
    })),
    
  setEmailAddress: email =>
  set(() => ({
    emailAddress: email
    })),

    setPhoneNumber: phoneNumber =>
    set(() => ({
      phone_number: phoneNumber
      })),
    
  setDuration: duration =>
    set(() => ({
      duration: duration
      })),

  setIntrestType: interestType =>
      set(() => ({
        interest_type: interestType
        })),

   setPlanName: plan_name =>
        set(() => ({
          planName: plan_name
          })),
          
    setTarget: target =>
        set(() => ({
          target: target
          })),

    setCreatePlanResponse: create_plan =>
        set(() => ({
            createPlanResponse: create_plan
            })),

   setWalletType: walletType =>
      set(() => ({
       wallet_type: walletType
     })),

     setIntrestDaysResponse: intrest_days_res =>
     set(() => ({
      intrest_day_response: intrest_days_res
    })),

    setPlanType: planType =>
    set(() => ({
     plan_type: planType
   })),
   
   setPlanDetailsId: planDetailId =>
   set(() => ({
    plan_detail_id: planDetailId
  })),

  // ONBOARDING
  setIntrestTypeOnboarding: interestTypeOnboarding =>
  set(() => ({
    interest_type_onboarding: interestTypeOnboarding
 })),
 
  setPlanNameOnboarding: planNameOnboarding =>
  set(() => ({
    plan_name_onboarding: planNameOnboarding
  })),

  setIntrestTypeTextOnboarding: interestTypeTextOnboarding =>
  set(() => ({
    interest_type_text_onboarding: interestTypeTextOnboarding
  })),

  setTargetOnboarding: targetOnboarding =>
  set(() => ({
    target_onboarding: targetOnboarding
  })),

  setDurationOnboarding: durationOnboarding =>
  set(() => ({
    duration_onboarding: durationOnboarding
  })),

  setTokenOnboarding: tokenOnboarding =>
  set(() => ({
    token_onboarding: tokenOnboarding
  })),

  setReferallCodeOnboading: referallCodeOnboarding =>
  set(() => ({
    referall_code_onboarding: referallCodeOnboarding
  })),

 
});


export const useOnboardingPlan = create<OnboardingPlanDataSlice>()(
  persist(
    (...a) => {
      return {
        ...createOnboardingPlanDataSlice(...a),
      };
    },
    {
      name: 'on-lending-store',
      storage: createJSONStorage(() => localStorage),
    },
  ),
);
