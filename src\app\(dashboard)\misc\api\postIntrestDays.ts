import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface PostIntrestDaysDTO {
  min_days: number;
  max_days: number;
}

const postIntrestDays = (postIntrestDaysDTO: PostIntrestDaysDTO,  email: string): Promise<AxiosResponse> => {
  const headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  
  
  return savingsAxios.post(`/onlending/interest-for-ranges/`, postIntrestDaysDTO, {headers});
};

export const usePostIntrestDaysOTP = (email: string) => {
  return useMutation('postIntrestDays', (postIntrestDaysDTO: PostIntrestDaysDTO) => postIntrestDays(postIntrestDaysDTO, email), {});
};
