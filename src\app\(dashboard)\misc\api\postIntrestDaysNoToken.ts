import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface PostIntrestDaysNoTokenDTO {
  min_days: number;
  max_days: number;
}

const postIntrestDaysNoToken = (postIntrestDaysNoTokenDTO: PostIntrestDaysNoTokenDTO,  email: string): Promise<AxiosResponse> => {
  const _headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  
  
  return savingsAxios.post(`/onlending/interest-for-ranges/`, postIntrestDaysNoTokenDTO,);
};

export const usePostIntrestDaysNoTokenOTP = (email: string) => {
  return useMutation('postIntrestDaysNoToken', (postIntrestDaysNoTokenDTO: PostIntrestDaysNoTokenDTO) => postIntrestDaysNoToken(postIntrestDaysNoTokenDTO, email), {});
};
