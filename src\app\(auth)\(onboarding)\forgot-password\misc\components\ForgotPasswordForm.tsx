"use client"
import { But<PERSON>, Input, <PERSON><PERSON>utton } from '@/components/core'
import { Label } from '@radix-ui/react-label'
import React from 'react'
import { useRequestResetPassword } from '../api/forgotPassword'
import { useRouter } from 'next/navigation'
import { getInputValueFromForm } from '@/utils/forms'

const ForgotPasswordForm = () => {
    const router = useRouter()
    const { mutate: requestResetPassword, isLoading: isRequestResetPasswordLoading, } = useRequestResetPassword()

    async function handleForgotPassword(event: React.FormEvent<HTMLFormElement>) {
        event.preventDefault();

        const form = event.target as HTMLFormElement;
        const email = getInputValueFromForm(form, 'email');

        const updatedData = {
            identifier: email,
            entry_type: "email"
        }

        requestResetPassword(updatedData, {
            onSuccess: (data) => {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                router.push(`/forgot-password/otp?email=${email}&phone=${data.data.phone_number}`)
            }
        })
    }
    return (
        <div className='relative z-10'>
            <form onSubmit={handleForgotPassword}>
                <Label className="sr-only" htmlFor="email">
                    Email
                </Label>
                <Input
                    className="login-autofill-text mb-10 login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
                    id="email"
                    name="email"
                    placeholder="Email"
                    type="email"
                    required
                />
                <Button
                    className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
                    disabled={isRequestResetPasswordLoading}
                    type="submit"
                    variant="white"
                >
                    {isRequestResetPasswordLoading ? 'Loading' : 'Done'}
                </Button>

                <LinkButton
                    className="mt-6 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
                    href="/login"
                    type="button"
                    variant="outlined"
                >

                    Back to Login
                </LinkButton>
            </form>
        </div>
    )
}

export default ForgotPasswordForm
