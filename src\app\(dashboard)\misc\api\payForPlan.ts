import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface PayForPlanDTO {
  plan_id: number;
  wallet_type: string;
  transaction_pin?: number;
  otp?: number;
}

export interface CreatePlanOTPResponse {
  message: string;
}

const payForPlan = (payForPlanDTO: PayForPlanDTO,  email: string): Promise<AxiosResponse> => {
  const headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  
  
  return savingsAxios.post(`/payment/pay-for-onlending-plan/`, payForPlanDTO, {headers});
};

export const usePlanForPlanOTP = (email: string) => {
  return useMutation('payForPlan', (payForPlanDTO: PayForPlanDTO) => payForPlan(payForPlanDTO, email), {});
};
