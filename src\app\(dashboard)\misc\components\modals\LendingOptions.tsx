'use client';

import React from 'react';
import { z } from 'zod';

import {
  <PERSON>alog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/core';
import Link from 'next/link';



interface LendingOptionsModalProps {
  isLendingOptionsOpen: boolean;
  setLendingOptionsState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
}

const LendingOptionsSchema = z.object({
  LendingOptions: z
    .string({ required_error: 'Please select a wallet type.' })
    .trim()
    .min(1, { message: 'Please select a wallet type.' }),
});

export type LendingOptionsValues = z.infer<typeof LendingOptionsSchema>;

export function LendingOptions({
  isLendingOptionsOpen,
  setLendingOptionsState,
  heading,
}: LendingOptionsModalProps) {


  return (
    <Dialog open={isLendingOptionsOpen} onOpenChange={setLendingOptionsState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="text-left">
          <p className='text-xs font-semibold'>Which of our products do you want to lend to?</p>

          <div className='space-y-2'>
            <Link className='flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]' href="/lending-options/lending-plan">
              <div className='flex gap-[10px] '>
                <div>
                  <p className='text-[#242424] text-sm font-semibold'>Public sector lending </p>
                  <p className='text-xs font-medium text-[#646464]'>Lend to government works with ease</p>
                </div>
              </div>

              <div>
                <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                  <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                </svg>
              </div>
            </Link>

            <Link className='flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]' href="/lending-options/lending-plan">
              <div className='flex gap-[10px] '>
                <div>
                  <p className='text-[#242424] text-sm font-semibold'>Agent network lending</p>
                  <p className='text-xs font-medium text-[#646464]'>Lend to business person and private employees</p>
                </div>
              </div>

              <div>
                <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                  <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                </svg>
              </div>
            </Link>
          </div>
        </DialogBody>
      </DialogContent>

    </Dialog>
  );
}
