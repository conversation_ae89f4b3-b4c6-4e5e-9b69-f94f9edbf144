import { savingsAxios } from '@/lib/axios';


import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface PostRnplDTO {
  full_name: string
  phone_number: string
  email: string
  location: string
  employment_status: string
  monthly_income: number
  loan_purpose: string
  annual_rent: number
  repayment_duration: string
  has_valid_id: boolean
  willing_to_provide_docs: boolean
}

const postRnpl = (postRnplDTO: PostRnplDTO): Promise<AxiosResponse> => {
  return savingsAxios.post(`/loans/api/v1/rnpl-wailtlist/`, postRnplDTO,);
};

export const usePostRnplOTP = () => {
  return useMutation('postRnpl', postRnpl, {});
};
