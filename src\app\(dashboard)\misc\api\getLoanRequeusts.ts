import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface LoanRequestsData {
  status: boolean
  data: LoanRequestsEntity[]
}

export interface LoanRequestsEntity {
  loan_amount: number
  status: string
  borrowers_name: string
  borrowers_ministry: string
}


export interface User {
  email: string
}

export const getLoanRequests = async (email: string): Promise<LoanRequestsData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get(`/onlending/loan-requests/`, {headers});
  return data;
};

export const useLoanRequests = (email: string) =>
  useQuery('all-loan-requests', () => getLoanRequests(email), );
 