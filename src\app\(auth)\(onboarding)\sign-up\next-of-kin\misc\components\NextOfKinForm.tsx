'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
// import type { AxiosError } from 'axios';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  Button,
  ErrorModal,
  FormError,
  Input,
  LinkButton,
  LoaderModal,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/core';

import { useBooleanStateControl, useErrorModalState } from '@/hooks';
// import { formatAxiosErrorMessage } from '@/utils/errors';

import { useRegisterNextOfKin } from '../api';
import React from 'react';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { SuccessMainModal } from '@/components/core/SuccessMainModal';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useRouter } from 'next/navigation';

const NextOfKinFormSchema = z.object({
  phone_number: z
    .string({ required_error: 'Please enter your phone number.' })
    .trim()
    .min(1, { message: 'Please enter your phone number.' }),
  full_name: z
    .string({ required_error: 'Please enter a full name.' })
    .trim()
    .min(1, { message: 'Please enter a full name.' }),
  email: z
    .string({ required_error: 'Please enter your email.' })
    .trim()
    .min(1, { message: 'Please enter your email.' }),
  address: z
    .string({ required_error: 'Please enter your address.' })
    .trim()
    .min(1, { message: 'Please enter your address.' }),
  relationship: z
    .string({ required_error: 'Please select a relationship.' })
    .trim()
    .min(1, { message: 'Please select a relationship.' }),

});

export type NextOfKinFormValues = z.infer<typeof NextOfKinFormSchema>;

interface CreateNextOfKinProps {
  phone: string;
  email: string
}

const relationshipOptions = [
  { name: 'Father', value: 'FATHER' },
  { name: 'Mother', value: 'MOTHER' },
  { name: 'Sibling', value: 'SIBLING' },
  { name: 'Cousin', value: 'COUSIN' },
  { name: 'Spouse', value: 'SPOUSE' },
];

export function NextOfKinForm({ }: CreateNextOfKinProps) {
  const router = useRouter()

  const { emailAddress } = useOnboardingPlan();

  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<NextOfKinFormValues>({
    resolver: zodResolver(NextOfKinFormSchema),
  });

  const {
    mutate: registerNextOfKin,
    isLoading: isRegisterNextOfKinLoading,
  } = useRegisterNextOfKin(emailAddress as string);

  const onGetStartedSubmit = (submittedData: NextOfKinFormValues) => {
    registerNextOfKin(
      { ...submittedData },
      {
        onSuccess: () => {

          openSuccessModal()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );

  };

  return (
    <>
      <LoaderModal isOpen={isRegisterNextOfKinLoading} />

      <form
        className="relative z-10"
        onSubmit={handleSubmit(onGetStartedSubmit)}
      >

        <Label className="sr-only" htmlFor="full_name">
          Next of Kin’s fullname
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg  h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="fullname"
          placeholder="Next of Kin’s fullname"
          type="text"
          {...register('full_name')}
        />
        {errors?.full_name && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.full_name.message}
          />
        )}

        <Label className="sr-only" htmlFor="phone_number">
          Next of Kin’s phone number
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="phone_number"
          placeholder="Next of Kin’s phone number"
          type="text"
          {...register('phone_number')}
        />
        {errors?.phone_number && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.phone_number.message}
          />
        )}

        <Label className="sr-only" htmlFor="email">
          Next of Kin’s email
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4  h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="email"
          placeholder="Next of Kin’s email"
          type="text"
          {...register('email')}
        />
        {errors?.email && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.email.message}
          />
        )}

        <Label className="sr-only" htmlFor="address">
          Next of Kin’s address
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="address"
          placeholder="Next of Kin’s address"
          type="text"
          {...register('address')}
        />
        {errors?.address && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.address.message}
          />
        )}

        <Label className="sr-only" htmlFor="relationship">
          Relationship
        </Label>
        <Controller
          control={control}
          name="relationship"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                iconClassName="fill-white"
                id="relationship"
                ref={ref}
              >
                <SelectValue placeholder="Relationship" />
              </SelectTrigger>
              <SelectContent>
                {relationshipOptions.map(({ name, value }) => {
                  return (
                    <SelectItem key={name} value={value}>
                      {name}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.relationship && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.relationship.message}
          />
        )}



        <Button
          className="mt-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isRegisterNextOfKinLoading}
          type="submit"
          variant="white"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isRegisterNextOfKinLoading ? 'Loading' : 'Next'}</span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>

        <Button
          className="mt-6 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
          type="button"
          variant="outlined"
          onClick={() => {
            router.back()
          }}
        >

          Back
        </Button>
      </form>

      <SuccessMainModal
        heading='Next of kin details submitted successfully'
        isSuccessMainModalOpen={isSuccessModalOpen}
        setSuccessMainModalState={setSuccessModalState}
        subheading=''
      >
        <div className="flex gap-3 rounded-2xl bg-green-50 px-8 py-6">
          <LinkButton className="py-3 w-full text-sm" href='/' size="fullWidth">
            Close
          </LinkButton>
        </div>
      </SuccessMainModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-blue-100 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
