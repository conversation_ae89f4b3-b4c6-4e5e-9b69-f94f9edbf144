import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';

export interface RegisterNextOfKinDto {
  phone_number: string;
  full_name: string;
  relationship: string;
  address: string;
  email: string;
}

const registerNextOfKin = (registerNextOfKinDto:RegisterNextOfKinDto, email: string): Promise<AxiosResponse> => {
  const headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  

  return savingsAxios.post(`/accounts/next-of-kin/`, registerNextOfKinDto, {headers});
};

export const useRegisterNextOfKin = ( email: string) => {
  return useMutation('registerNextOfKin',(registerNextOfKinDto:RegisterNextOfKinDto)=> registerNextOfKin(registerNextOfKinDto,email), {});
};


