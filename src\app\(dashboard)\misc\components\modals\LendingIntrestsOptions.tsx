'use client';

import React from 'react';
import { z } from 'zod';

import {
  <PERSON>ton,
  <PERSON>alog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  LoaderModal,
} from '@/components/core';

import { useErrorModalState } from '@/hooks';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useCreatePlanOTP } from '../../api/createPlan';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';



interface LendingIntrestsOptionsModalProps {
  isLendingIntrestsOptionsOpen: boolean;
  setLendingIntrestsOptionsState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
}

const LendingIntrestsOptionsSchema = z.object({
  LendingIntrestsOptions: z
    .string({ required_error: 'Please select a wallet type.' })
    .trim()
    .min(1, { message: 'Please select a wallet type.' }),
});

export type LendingIntrestsOptionsValues = z.infer<typeof LendingIntrestsOptionsSchema>;

export function LendingIntrestsOptions({
  isLendingIntrestsOptionsOpen,
  setLendingIntrestsOptionsState,
  heading,
}: LendingIntrestsOptionsModalProps) {

  const router = useRouter()

  const { target, duration, emailAddress, setIntrestType, planName, setCreatePlanResponse } = useOnboardingPlan();

  const { mutate: createPlan, isLoading: isCreatePlanLoading } =
    useCreatePlanOTP(emailAddress as string);

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const initiateCreatePlan = (IntrestType: string) => {
    createPlan(
      {
        duration: Number(duration),
        interest_type: String(IntrestType),
        name: String(planName),
        target: Number(target),
      },
      {
        onSuccess: (data) => {
          setCreatePlanResponse({
            duration: data.data.data.duration,
            id: data.data.data.id,
            name: data.data.data.name,
            quotation_id: data.data.data.quotation_id,
            target: data.data.data.target,
            interest_type: data.data.data.interest_type,
            maturity_date: data.data.data.maturity_date
          })

          router.push("/lending-options/summary")

        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );


  }
  return (
    <>

      <LoaderModal
        isOpen={
          isCreatePlanLoading
        }
      />
      <Dialog open={isLendingIntrestsOptionsOpen} onOpenChange={setLendingIntrestsOptionsState}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
            <DialogClose className="ml-auto">Close</DialogClose>
          </DialogHeader>

          <DialogBody className="text-left">
            <p className='text-xs font-semibold'>When do you want to receive your interest ?</p>

            <div className='space-y-2'>

              <div className='flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px] cursor-pointer' onClick={() => {
                setIntrestType("UPFRONT")
                initiateCreatePlan("UPFRONT")
              }}>
                <div className='flex gap-[10px] '>
                  <div>
                    <p className='text-[#242424] text-sm font-semibold'>Take interest up-front</p>
                    <p className='text-xs font-medium text-[#646464]'>Here you get interest paid to you onlending plan creation</p>
                  </div>
                </div>

                <div>
                  <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                  </svg>
                </div>
              </div>

              <div className='cursor-pointer flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]' onClick={() => {
                setIntrestType("MATURITY_DATE")
                initiateCreatePlan("MATURITY_DATE")
              }}>

                <div className='flex gap-[10px] '>
                  <div>
                    <p className='text-[#242424] text-sm font-semibold'>Take interest on maturity</p>
                    <p className='text-xs font-medium text-[#646464]'>Here you get to take your interest when a lending plan closes</p>
                  </div>
                </div>

                <div>
                  <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                  </svg>
                </div>
              </div>

            </div>
          </DialogBody>
        </DialogContent>

        <ErrorModal
          isErrorModalOpen={isErrorModalOpen}
          setErrorModalState={setErrorModalState}
          subheading={
            errorModalMessage || 'Please check your inputs and try again.'
          }
        >
          <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
            <Button
              className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
              size="lg"
              type="button"
              onClick={closeErrorModal}
            >
              Okay
            </Button>
          </div>
        </ErrorModal>

      </Dialog>
    </>
  );
}
