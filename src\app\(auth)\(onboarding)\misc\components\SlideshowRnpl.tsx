'use client';

import { motion } from 'framer-motion';
import * as React from 'react';

import { LinkButton } from '@/components/core/LinkButton';
import { shiftArrayItemsByOne } from '@/utils/arrays';
import { cn } from '@/utils/classNames';
// import SeedsPennies from "@/icons/SeedsPenniesLogo";
import Link from 'next/link';
import SeedsPenniesLogo from '@/icons/SeedsPenniesLogo';

const slideshowContent = [
  {
    heading: 'Rent Now, Pay Later',
    text: 'Need help paying your rent upfront? Get access to a flexible rent loan and spread your payments over convenient monthly installments.',
  },
  {
    heading: 'Easy Application',
    text: 'Quick and simple application process. Get approved in minutes and receive your rent payment directly to your landlord.',
  },
  {
    heading: 'Flexible Terms',
    text: 'Choose payment terms that work for you. Split your rent into manageable monthly payments with competitive rates.',
  },
  {
    heading: 'Peace of Mind',
    text: 'No more stress about upfront rent payments. Our service helps you maintain your housing stability while managing your finances better.',
  },
];

export function SlideshowRnpl() {
  const [currentSlideshow, setCurrentSlideshow] =
    React.useState(slideshowContent);

  React.useEffect(() => {
    const interval = setInterval(() => {
      const shiftedSlideshow = shiftArrayItemsByOne(currentSlideshow);
      setCurrentSlideshow(shiftedSlideshow);
    }, 3500); // Change the active index every 5 seconds

    return () => clearInterval(interval);
  }, [currentSlideshow]);

  return (
    <div className="relative hidden md:block mx-auto w-full max-w-[32.375rem] shrink-0 overflow-hidden text-white md:h-screen-small md:max-w-none md:basis-1/2 md:pb-0 md:[@media(max-height:520px)]:overflow-y-auto">
      <header className="absolute top-0 z-10 hidden w-full items-center justify-between  px-6 py-11 md:flex xl:px-10">
      <Link className={cn("hidden md:flex font-semibold text-xl lg:text-2xl relative z-50")} href="/">
            <SeedsPenniesLogo />
          </Link>

        <LinkButton
          className="hidden gap-2 px-5 py-2 text-base"
          href="/"
          size="unstyled"
          variant="white"
        >
          <span>
            <svg
              fill="none"
              height={24}
              viewBox="0 0 24 24"
              width={24}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m20.83 8.01-6.55-5.24C13 1.75 11 1.74 9.73 2.76L3.18 8.01c-.94.75-1.51 2.25-1.31 3.43l1.26 7.54C3.42 20.67 4.99 22 6.7 22h10.6c1.69 0 3.29-1.36 3.58-3.03l1.26-7.54c.18-1.17-.39-2.67-1.31-3.42Z"
                fill="#032282"
              />
              <path
                d="M12 18.75c-.41 0-.75-.34-.75-.75v-3c0-.41.34-.75.75-.75s.75.34.75.75v3c0 .41-.34.75-.75.75Z"
                fill="#fff"
              />
            </svg>
          </span>

          <span>Home</span>
        </LinkButton>
      </header>

      <aside className="md:h-screen">
        {currentSlideshow.map(({ heading, text }, index) => {
          const isLastItem = index + 1 === currentSlideshow.length;

          const isEven = (index + 1) % 2 === 0;

          return (
            <motion.div
              animate={{
                opacity: !isLastItem ? 0.2 : 1, // Fade in the current slide
                y: !isLastItem ? 20 : 0, // Animate from 20px below for current slide
              }}
              className={cn(
                'w-full p-6 md:flex md:flex-col md:justify-center xl:px-10 md:[@media(min-height:520px)]:h-1/4',
                !isLastItem && 'hidden md:block',
                index === 0 && 'bg-[#000000]/90',
                index === 1 && 'bg-[#00114B]/60'
              )}
              initial={{ opacity: 0.5, y: 20 }} // Initial state (hidden)
              key={heading}
              transition={{ duration: 1 }} // Animation duration
            >
              <div className={cn('w-max ', !isEven && 'ml-auto')}>
                <h2
                  className={cn(
                    'mb-4 font-clash text-3xl font-semibold leading-[78.5%] md:text-4xl',
                    isLastItem && 'xl:text-5xl'
                  )}
                >
                  {heading}
                </h2>
                <p className="leading-[123.5% max-w-[22.0625rem] font-clash">
                  {text}
                </p>
              </div>
            </motion.div>
          );
        })}
      </aside>
    </div>
  );
}
