import { OnboardingPageWrapper } from "../../misc/components";
import ResetPasswordForm from "../misc/components/ResetPasswordForm";


export default function ForgotPassword({
    searchParams,
}: {
    searchParams: { email: string; phone: string; otp: string };
}) {
    const { email, phone, otp } = searchParams;

    return (
        <OnboardingPageWrapper
            heading="Forgot Password?"
            subHeading="Enter your email address and we will send an email with a link to reset your password."
        >
            <ResetPasswordForm email={email} otp={otp} phone={phone} />
        </OnboardingPageWrapper>
    );
}