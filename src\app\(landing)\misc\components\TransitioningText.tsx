import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface TransitioningTextProps {
    texts: string[];
    interval?: number;
    className?: string;
}

const TransitioningText: React.FC<TransitioningTextProps> = ({ texts, interval = 4000, className }) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length);
        }, interval);

        return () => clearInterval(timer);
    }, [texts, interval]);



    return (
        <div className="relative text-[clamp(2.2rem,3.5vw,300px)] h-[3lh] sm:h-[2lh] overflow-hidden text-left md:text-center max-w-[21ch] max-md:pr-4">
            <AnimatePresence mode="wait">
                <motion.h1
                    animate={{ opacity: 1, y: 0 }}
                    className={className}
                    exit={{ opacity: 0, y: -20 }}
                    initial={{ opacity: 0, y: 20 }}
                    key={currentIndex}
                    transition={{ duration: 0.5 }}
                >
                    {texts[currentIndex]}
                </motion.h1>
            </AnimatePresence>
        </div>
    );
};

export default TransitioningText;