import * as React from 'react';

import { OnboardingPageWrapper } from '../../misc/components';
import { EmailOTPForm } from './misc/components';

export default function EmailOTP({
  searchParams,
}: {
  searchParams: { email: string; phone: string };
}) {
  const { email, phone } = searchParams;

  return (
    <>
      <div className="mx-auto max-w-[32.375rem] px-2 md:hidden">
        <p className="relative mb-2 ml-auto w-max text-white md:hidden">2/7</p>
      </div>

      <OnboardingPageWrapper
        heading="Verify"
        subHeading={`Please enter the passcode sent to your email (${email}) to complete your registration.`}
      >
        <EmailOTPForm email={email} phone={phone} />
      </OnboardingPageWrapper>
    </>
  );
}
