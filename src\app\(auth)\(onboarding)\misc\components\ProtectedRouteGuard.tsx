'use client';

import * as React from 'react';

import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/authentication'; // Import your authentication context

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRouteGuard({ children }: ProtectedRouteProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { authState } = useAuth();

  const protectedRoutes = ['/']; // Define your protected routes here

  const { isAuthenticated, isLoading } = authState;

  const path = pathname; // Access pathname using useRouter

  React.useEffect(() => {
    if (!isLoading && !isAuthenticated && protectedRoutes.includes(path)) {
      router.push('/login');
    }
  }, [isLoading, isAuthenticated, path, router]);

  if ((isLoading || !isAuthenticated) && protectedRoutes.includes(path)) {
    return (
      <div className="flex h-screen w-screen bg-[url('/images/loading_screen.png')] items-center justify-center">

        <div className="flex h-screen w-screen bg-[#0000008f] items-center justify-center">

          <div className="flex size-40 animate-bounce items-center  justify-center rounded-full bg-purple-3 p-6">
            <div className="animate-pulse">
              <div className="w-[137px] h-[26.35px]">
                <svg fill="none" height="25" viewBox="0 0 144 25" width="144" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11.696 24.34C4.794 24.34 0.918 21.62 0.918 16.316V16.112H6.018V16.724C6.018 18.832 7.072 19.682 11.696 19.682C15.776 19.682 16.728 19.07 16.728 17.54C16.728 16.146 15.946 15.636 13.6 15.228L7.208 14.31C3.128 13.664 0.714 11.624 0.714 7.918C0.714 4.484 3.502 0.88 11.152 0.88C18.156 0.88 21.42 4.11 21.42 8.904V9.108H16.286V8.632C16.286 6.456 15.164 5.538 10.642 5.538C6.97 5.538 5.848 6.252 5.848 7.714C5.848 9.04 6.596 9.516 8.5 9.856L14.892 10.876C19.992 11.692 21.828 14.174 21.828 17.302C21.828 21.008 18.938 24.34 11.696 24.34ZM33.6289 24.34C27.9509 24.34 24.0749 21.552 24.0749 15.5C24.0749 10.06 27.9169 6.626 33.5269 6.626C39.1029 6.626 42.8089 9.55 42.8089 14.888C42.8089 15.5 42.7409 15.942 42.6729 16.52H28.8009C28.9369 19.138 30.1609 20.26 33.4589 20.26C36.4849 20.26 37.5389 19.478 37.5389 18.016V17.676H42.6389V18.05C42.6389 21.756 39.0349 24.34 33.6289 24.34ZM33.4249 10.604C30.3989 10.604 29.1069 11.624 28.8689 13.834H37.9129C37.7769 11.59 36.4169 10.604 33.4249 10.604ZM54.6956 24.34C49.0176 24.34 45.1416 21.552 45.1416 15.5C45.1416 10.06 48.9836 6.626 54.5936 6.626C60.1696 6.626 63.8756 9.55 63.8756 14.888C63.8756 15.5 63.8076 15.942 63.7396 16.52H49.8676C50.0036 19.138 51.2276 20.26 54.5256 20.26C57.5516 20.26 58.6056 19.478 58.6056 18.016V17.676H63.7056V18.05C63.7056 21.756 60.1016 24.34 54.6956 24.34ZM54.4916 10.604C51.4656 10.604 50.1736 11.624 49.9356 13.834H58.9796C58.8436 11.59 57.4836 10.604 54.4916 10.604ZM74.3344 24.34C69.0304 24.34 66.1064 20.872 66.1064 15.5C66.1064 10.06 68.9964 6.626 74.0624 6.626C78.1084 6.626 80.1824 8.632 80.7604 11.488H81.0664V1.22H86.1664V24H81.4064V19.274H81.1344C80.4884 22.64 78.2444 24.34 74.3344 24.34ZM71.2744 15.5C71.2744 18.594 72.8044 19.682 76.0684 19.682C79.2984 19.682 81.0664 18.56 81.0664 15.602V15.33C81.0664 12.372 79.3324 11.284 76.0684 11.284C72.8044 11.284 71.2744 12.372 71.2744 15.5ZM98.1468 24.34C92.4008 24.34 89.2388 22.096 89.2388 18.152V18.05H94.3388V18.356C94.3388 19.886 95.2908 20.294 98.1808 20.294C100.901 20.294 101.547 19.852 101.547 18.832C101.547 17.88 101.037 17.608 99.0308 17.336L94.2368 16.758C90.8368 16.384 88.9328 14.854 88.9328 11.998C88.9328 9.006 91.4828 6.626 97.3308 6.626C102.907 6.626 106.069 8.734 106.069 12.882V12.984H100.969V12.78C100.969 11.386 100.289 10.672 97.1608 10.672C94.6108 10.672 93.9648 11.114 93.9648 12.202C93.9648 13.086 94.4408 13.46 96.6848 13.732L100.323 14.174C104.879 14.684 106.579 16.214 106.579 19.036C106.579 22.232 103.417 24.34 98.1468 24.34ZM115.539 24H108.977V18.05H115.539V24ZM124.818 24H118.256V18.05H124.818V24ZM134.098 24H127.536V18.05H134.098V24ZM143.377 24H136.815V18.05H143.377V24Z" fill="white" />
                </svg>
              </div>
            </div>
          </div>

        </div>
      </div>
    );
  }

  return <>{children}</>;
}