import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface CreatePlanDTO {
  name: string;
  phone_number?: string;
  target: number;
  duration: number;
  interest_type: string;
}

export interface CreatePlanOTPResponse {
  message: string;
}

const createPlanOTP = (createPlanDTO: CreatePlanDTO,  email: string): Promise<AxiosResponse> => {
  const headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  
  
  return savingsAxios.post(`/onlending/create-edit-plan/`, createPlanDTO, {headers});
};

export const useCreatePlanOTP = (email: string) => {
  return useMutation('createPlanOTP', (createPlanDTO: CreatePlanDTO) => createPlanOTP(createPlanDTO, email), {});
};
