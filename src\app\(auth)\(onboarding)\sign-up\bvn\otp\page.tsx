import * as React from 'react';

import { OnboardingPageWrapper } from '../../../misc/components';
import { BVNOTPForm } from '../misc/components';

export default function BVNOTP({
  searchParams,
}: {
  searchParams: { phone: string };
}) {
  const { phone } = searchParams;

  return (
    <>
      <div className="mx-auto max-w-[32.375rem] px-2 md:hidden">
        <p className="relative mb-2 ml-auto w-max text-white md:hidden">6/7</p>
      </div>

      <OnboardingPageWrapper
        heading="OTP Verification"
        subHeading="An OTP code has been sent to the phone number linked to your BVN"
      >
        <BVNOTPForm phone={phone} />
      </OnboardingPageWrapper>
    </>
  );
}
