'use client';

import { LinkButton, LoaderModal } from '@/components/core';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import React from 'react';
import { useAllPlans } from '../../api/getAllPlans';
import { SaveIntrestsOptions } from '../modals/saveIntrestsOptions';
import { useBooleanStateControl } from '@/hooks';




interface InActivePlansProps {
}

export function InActivePlans({ }: InActivePlansProps) {
    const { emailAddress, setCreatePlanResponse } = useOnboardingPlan();

    const { data, isLoading } = useAllPlans(emailAddress as string, "inactive");

    const {
        state: isSaveIntrestOptionsOpen,
        setState: setSaveIntrestOptionsState,
        setTrue: _openSaveIntrestOptions,
    } = useBooleanStateControl();

    return (
        <>
            <LoaderModal
                isOpen={
                    isLoading
                }
            />

            <div className='flex items-center flex-col justify-between w-full pt-[20px] pb-[14px]'>
                {data?.results.length === 0 ? (<>
                    <p className='font-medium text-[#646464] text-right text-sm'>No inactive plans</p>
                </>) : (<>
                    {data?.results.map(({ name, id, duration, target, quotation_id, maturity_date, interest_type }) => {

                        return (
                            <>
                                <div className='flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]'>
                                    <div className='flex gap-[10px] '>
                                        <svg fill="none" height="34" viewBox="0 0 34 34" width="34" xmlns="http://www.w3.org/2000/svg">
                                            <rect fill="#E9FFF0" height="34" rx="17" width="34" />
                                            <path d="M22 15.9582C21.6583 15.9582 21.375 15.6748 21.375 15.3332V13.6665C21.375 11.0415 20.6333 9.2915 17 9.2915C13.3667 9.2915 12.625 11.0415 12.625 13.6665V15.3332C12.625 15.6748 12.3417 15.9582 12 15.9582C11.6583 15.9582 11.375 15.6748 11.375 15.3332V13.6665C11.375 11.2498 11.9583 8.0415 17 8.0415C22.0417 8.0415 22.625 11.2498 22.625 13.6665V15.3332C22.625 15.6748 22.3417 15.9582 22 15.9582Z" fill="#01AE53" />
                                            <path d="M21.1667 25.9585H12.8334C9.15841 25.9585 8.04175 24.8418 8.04175 21.1668V19.5002C8.04175 15.8252 9.15841 14.7085 12.8334 14.7085H21.1667C24.8417 14.7085 25.9584 15.8252 25.9584 19.5002V21.1668C25.9584 24.8418 24.8417 25.9585 21.1667 25.9585ZM12.8334 15.9585C9.85008 15.9585 9.29175 16.5252 9.29175 19.5002V21.1668C9.29175 24.1418 9.85008 24.7085 12.8334 24.7085H21.1667C24.1501 24.7085 24.7084 24.1418 24.7084 21.1668V19.5002C24.7084 16.5252 24.1501 15.9585 21.1667 15.9585H12.8334Z" fill="#01AE53" />
                                            <path d="M13.6666 21.1667C13.5583 21.1667 13.4499 21.1417 13.3499 21.1001C13.2416 21.0584 13.1583 21 13.0749 20.925C12.9249 20.7667 12.8333 20.5584 12.8333 20.3334C12.8333 20.2251 12.8582 20.1167 12.8999 20.0167C12.9416 19.9084 12.9999 19.8251 13.0749 19.7417C13.1583 19.6667 13.2416 19.6084 13.3499 19.5667C13.6499 19.4334 14.0249 19.5084 14.2582 19.7417C14.3332 19.8251 14.3916 19.9167 14.4333 20.0167C14.4749 20.1167 14.4999 20.2251 14.4999 20.3334C14.4999 20.5501 14.4082 20.7667 14.2582 20.925C14.0999 21.075 13.8833 21.1667 13.6666 21.1667Z" fill="#01AE53" />
                                            <path d="M17.0001 21.1669C16.7834 21.1669 16.5668 21.0752 16.4084 20.9252C16.2584 20.7669 16.1667 20.5586 16.1667 20.3336C16.1667 20.2253 16.1834 20.1169 16.2334 20.0169C16.2751 19.9169 16.3334 19.8253 16.4084 19.7419C16.6001 19.5503 16.8918 19.4586 17.1584 19.5169C17.2168 19.5252 17.2668 19.5419 17.3168 19.5669C17.3668 19.5836 17.4167 19.6086 17.4667 19.6419C17.5084 19.6669 17.5501 19.7086 17.5917 19.7419C17.6667 19.8253 17.7251 19.9169 17.7668 20.0169C17.8084 20.1169 17.8334 20.2253 17.8334 20.3336C17.8334 20.5586 17.7417 20.7669 17.5917 20.9252C17.5501 20.9586 17.5084 20.9919 17.4667 21.0252C17.4167 21.0586 17.3668 21.0836 17.3168 21.1003C17.2668 21.1253 17.2168 21.1419 17.1584 21.1503C17.1084 21.1586 17.0501 21.1669 17.0001 21.1669Z" fill="#01AE53" />
                                            <path d="M20.3333 21.1669C20.1083 21.1669 19.9 21.0752 19.7417 20.9252C19.6667 20.8419 19.6083 20.7503 19.5667 20.6503C19.525 20.5503 19.5 20.4419 19.5 20.3336C19.5 20.1169 19.5917 19.9003 19.7417 19.7419C19.7833 19.7086 19.825 19.6753 19.8667 19.6419C19.9167 19.6086 19.9667 19.5836 20.0167 19.5669C20.0667 19.5419 20.1167 19.5252 20.1667 19.5169C20.4417 19.4586 20.725 19.5503 20.925 19.7419C21.075 19.9003 21.1667 20.1086 21.1667 20.3336C21.1667 20.4419 21.1417 20.5503 21.1 20.6503C21.0583 20.7586 21 20.8419 20.925 20.9252C20.7667 21.0752 20.55 21.1669 20.3333 21.1669Z" fill="#01AE53" />
                                        </svg>

                                        <div className=''>
                                            <p className='text-[#242424] text-sm font-semibold'>{name}</p>
                                            <p className='text-xxs font-medium text-[#646464]'>Status - <span className='text-[#FF6B00]'>Inactive</span></p>
                                        </div>
                                    </div>

                                    {/* <div>
                                        <p className='text-[#01AE53] text-sm font-semibold text-right'>{amountNumberFormat(amount_saved)}</p>
                                        <p className='text-xs font-medium text-[#646464] text-right'>Maturity <span>{maturity_date} {hour}</span></p>
                                    </div> */}
                                    <LinkButton className="bg-[#F1F8FF] py-[8px] px-[10px] text-xxs text-[#032282]" href="/lending-options/activate-plan"
                                        onClick={() => {
                                            setCreatePlanResponse({
                                                id: id,
                                                name: name,
                                                duration: duration,
                                                target: target,
                                                quotation_id: quotation_id,
                                                interest_type: interest_type,
                                                maturity_date: maturity_date
                                            })
                                            // openSaveIntrestOptions()
                                        }}>Activate</LinkButton>
                                </div>

                                <SaveIntrestsOptions
                                    email={String(emailAddress)}
                                    heading='Save'
                                    isSaveIntrestsOptionsOpen={isSaveIntrestOptionsOpen}
                                    setSaveIntrestsOptionsState={setSaveIntrestOptionsState}
                                />

                            </>
                        )
                    })}</>)}
            </div>
        </>
    );
}
