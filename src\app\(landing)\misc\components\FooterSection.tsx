import Link from "next/link"
import Image from "next/image"
import { Facebook, Instagram, Linkedin, Twitter, Youtube } from 'lucide-react'
import SeedsLogoHorizontal from "./SeedsLogoHorizontal"

export default function Footer() {
    return (
        <footer className="bg-[#000619] text-white pt-16">
            <div className="container mx-auto px-4">
                <div className="flex flex-col items-center space-y-8 md:space-y-12">
                    {/* Logo */}
                    <Link className="flex justify-center font-mono" href="/" >
                        <SeedsLogoHorizontal className="w-[90%] max-w-[400px]" />
                    </Link>

                    {/* Navigation and Social Links */}
                    <div className="flex flex-col md:flex-row items-center gap-8 w-full justify-between">
                        {/* Left Social Icons */}
                        <div className="flex items-center gap-4">
                            <Link
                                aria-label="Twitter"
                                className="p-3 rounded-full bg-neutral-800 hover:bg-neutral-700 transition-colors"
                                href="#"
                            >
                                <Twitter className="w-5 h-5" />
                            </Link>
                            <Link
                                aria-label="Telegram"
                                className="p-3 rounded-full bg-neutral-800 hover:bg-neutral-700 transition-colors"
                                href="#"
                            >
                                <Youtube className="w-5 h-5" />
                            </Link>
                            <Link
                                aria-label="Telegram"
                                className="p-3 rounded-full bg-neutral-800 hover:bg-neutral-700 transition-colors invisible max-md:hidden"
                                href="#"
                            >
                                <Youtube className="w-5 h-5" />
                            </Link>
                        </div>

                        {/* Navigation */}
                        <nav className="flex items-center justify-center flex-wrap gap-8 xl:gap-12 gap-y-5">
                            <Link
                                className="text-white/80 hover:text-white transition-colors"
                                href="#"
                            >
                                About Us
                            </Link>
                            <Link
                                className="text-white/80 hover:text-white transition-colors"
                                href="/"
                            >
                                Products
                            </Link>
                            <Link
                                className="text-white/80 hover:text-white transition-colors"
                                href="/"
                            >
                                Careers
                            </Link>
                            <Link
                                className="text-white/80 hover:text-white transition-colors"
                                href="/"
                            >
                                Contact us
                            </Link>
                        </nav>

                        {/* Right Social Icons */}
                        <div className="flex items-center gap-4">
                            <Link
                                aria-label="Instagram"
                                className="p-3 rounded-full bg-neutral-800 hover:bg-neutral-700 transition-colors"
                                href="#"
                            >
                                <Instagram className="w-5 h-5" />
                            </Link>
                            <Link
                                aria-label="Facebook"
                                className="p-3 rounded-full bg-neutral-800 hover:bg-neutral-700 transition-colors"
                                href="#"
                            >
                                <Facebook className="w-5 h-5" />
                            </Link>
                            <Link
                                aria-label="LinkedIn"
                                className="p-3 rounded-full bg-neutral-800 hover:bg-neutral-700 transition-colors"
                                href="#"
                            >
                                <Linkedin className="w-5 h-5" />
                            </Link>
                        </div>
                    </div>

                    {/* Bottom Section */}
                    <div className="w-full flex flex-col md:flex-row justify-between items-center gap-4 pt-8 border-t border-white/10 text-center">
                        <p className="text-white/60 text-sm">
                            Copyright © 2024, Seeds & Pennies. All rights reserved
                        </p>
                        <div className="flex items-center gap-6">
                            <Link
                                className="text-white/60 hover:text-white text-sm transition-colors"
                                href="/privacy-policy"
                            >
                                Privacy Policy
                            </Link>
                            <Link
                                className="text-white/60 hover:text-white text-sm transition-colors"
                                href="/privacy-policy"
                            >
                                Terms of Service
                            </Link>
                        </div>
                    </div>
                </div>
            </div>


            <div className="relative h-[12.5vh] max-h-[205px] w-screen mt-4 md:mt-8 md:border-t-[0.15px] border-t-white/30 ">
                <div className="absolute w-full top-0 left-0 bottom-0 right-0">
                    <Image
                        alt='footer-pattern'
                        className='py-2 md:py-3'
                        layout="fill"
                        objectFit="cover"
                        quality={100}
                        src='/images/landing/footer-pattern.svg'
                    />
                </div>
            </div>
        </footer>
    )
}

