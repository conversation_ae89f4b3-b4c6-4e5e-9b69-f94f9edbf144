'use client'


import { useRef } from 'react';
import { useTransform, motion, MotionValue } from 'framer-motion';
import Link from 'next/link';

import { cn } from '@/utils/classNames';
import CaretDownIcon from '@/icons/core/CaretDown';

interface TestCardProps {
    i: number;
    title: string;
    content: string;
    link: string;
    cta_text: string;
    link_text: string;
    bg_color: string;
    text_color: string;
    progress: MotionValue<number>;
    range: [number, number];
    targetScale: number;
}

const StackingCard: React.FC<TestCardProps> = ({ i, title, content, link, bg_color, link_text, cta_text, text_color, progress, range, targetScale }) => {

    const container = useRef(null);
    const scale = useTransform(progress, range, [1, targetScale]);
    const isIndexOdd = i % 2 === 0;


    return (

        <div className="h-[60vh] md:h-screen flex flex-col items-center justify-center top-0 w-[90vw] md:px-5 xl:px-12 max-w-[1300px] mx-auto" ref={container}>
            <motion.div
                // className="flex max-md:flex-col max-md:mx-2.5 relative top-[-25%] h-[500px] md:h-[480px] w-[1000px] xl:w-[1100px] rounded-[3rem] px-2.5 md:px-10 transform origin-top"
                className={cn(
                    "flex flex-col gap-2 max-md:mx-2.5 relative top-[-25%] md:h-[280px] w-full md:w-[450px]  rounded-[1.5rem] p-4 md:p-6 transform origin-top bg-black/50 md:bg-[#FFFFFF0D]",
                    isIndexOdd ? "lg:mr-auto" : "lg:ml-auto", 'backdrop-blur-lg '
                )}
                style={{ scale, top: `calc(-5vh + ${i * 25}px)` }}
            >

                <span className="px-3 py-1 text-sm rounded-md max-w-max" style={{ backgroundColor: bg_color, color: text_color }}>
                    {cta_text}
                </span>
                <h4 className='text-2xl font-medium text-white'>
                    {title}
                </h4>
                <p className="text-sm md:text-[0.9rem] text-[#CDD5DD] leading-relaxed">
                    {content}
                </p>

                <Link className='flex items-center gap-2 mt-auto hover:underline' href={link} style={{ color: text_color, textDecorationColor: text_color }}>
                    {link_text}
                    <CaretDownIcon className='-rotate-90 ' style={{ fill: text_color,  }}/>
                </Link>
            </motion.div>

        </div>

    )

}

export default StackingCard
