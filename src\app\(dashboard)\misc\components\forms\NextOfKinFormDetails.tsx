'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
// import type { AxiosError } from 'axios';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  Button,
  ErrorModal,
  FormError,
  Input,
  LinkButton,
  LoaderModal,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/core';

import { useBooleanStateControl, useErrorModalState } from '@/hooks';

import React from 'react';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { SuccessMainModal } from '@/components/core/SuccessMainModal';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useRegisterNextOfKin } from '@/app/(auth)/(onboarding)/sign-up/next-of-kin/misc/api';

const NextOfKinFormDetailsSchema = z.object({
  phone_number: z
    .string({ required_error: 'Please enter your phone number.' })
    .trim()
    .min(1, { message: 'Please enter your phone number.' }),
  full_name: z
    .string({ required_error: 'Please enter a full name.' })
    .trim()
    .min(1, { message: 'Please enter a full name.' }),
  email: z
    .string({ required_error: 'Please enter your email.' })
    .trim()
    .min(1, { message: 'Please enter your email.' }),
  address: z
    .string({ required_error: 'Please enter your address.' })
    .trim()
    .min(1, { message: 'Please enter your address.' }),
  relationship: z
    .string({ required_error: 'Please select a relationship.' })
    .trim()
    .min(1, { message: 'Please select a relationship.' }),

});

export type NextOfKinFormDetailsValues = z.infer<typeof NextOfKinFormDetailsSchema>;

interface CreateNextOfKinProps {
  phone: string;
  email: string
}

const relationshipOptions = [
  { name: 'Father', value: 'FATHER' },
  { name: 'Mother', value: 'MOTHER' },
  { name: 'Sibling', value: 'SIBLING' },
];

export function NextOfKinFormDetails({ }: CreateNextOfKinProps) {

  const { emailAddress } = useOnboardingPlan();

  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<NextOfKinFormDetailsValues>({
    resolver: zodResolver(NextOfKinFormDetailsSchema),
  });

  const {
    mutate: registerNextOfKin,
    isLoading: isRegisterNextOfKinLoading,
  } = useRegisterNextOfKin(emailAddress as string);

  const onGetStartedSubmit = (submittedData: NextOfKinFormDetailsValues) => {
    registerNextOfKin(
      { ...submittedData },
      {
        onSuccess: () => {

          openSuccessModal()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );

  };

  return (
    <>
      <LoaderModal isOpen={isRegisterNextOfKinLoading} />

      <form
        className="relative z-10 mt-3 space-y-3"
        onSubmit={handleSubmit(onGetStartedSubmit)}
      >
        <div>
          <p className='text-xs text-[#4E4E4E] mb-[6px]'> Next of Kin’s fullname</p>
          <Input
            id="fullname"
            placeholder="Next of Kin’s fullname"
            type="text"
            {...register('full_name')}
          />
          {errors?.full_name && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.full_name.message}
            />
          )}
        </div>


        <div>
          <p className='text-xs text-[#4E4E4E] mb-[6px]'> Next of Kin’s phone number </p>

          <Input
            id="phone_number"
            placeholder="Next of Kin’s phone number"
            type="text"
            {...register('phone_number')}
          />
          {errors?.phone_number && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.phone_number.message}
            />
          )}
        </div>

        <div>
          <p className='text-xs text-[#4E4E4E] mb-[6px]'> Next of Kin’s email </p>

          <Input
            id="email"
            placeholder="Next of Kin’s email"
            type="text"
            {...register('email')}
          />
          {errors?.email && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.email.message}
            />
          )}
        </div>


        <div>
          <p className='text-xs text-[#4E4E4E] mb-[6px]'> Next of Kin’s address </p>
          <Input

            id="address"
            placeholder="Next of Kin’s address"
            type="text"
            {...register('address')}
          />
          {errors?.address && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.address.message}
            />
          )}
        </div>


        <div>
          <p className='text-xs text-[#4E4E4E] mb-[6px]'> Relationship </p>
          <Controller
            control={control}
            name="relationship"
            render={({ field: { onChange, value, ref } }) => (
              <Select value={value} onValueChange={onChange}>
                <SelectTrigger
                  className="ex h-10 w-full rounded-md bg-input-bg px-5 py-2 text-xs transition duration-300 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  iconClassName="fill-white"
                  id="relationship"
                  ref={ref}
                >
                  <SelectValue placeholder="Relationship" />
                </SelectTrigger>
                <SelectContent>
                  {relationshipOptions.map(({ name, value }) => {
                    return (
                      <SelectItem key={name} value={value}>
                        {name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            )}
          />
          {errors?.relationship && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.relationship.message}
            />
          )}
        </div>





        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isRegisterNextOfKinLoading}
          type="submit"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isRegisterNextOfKinLoading ? 'Loading' : 'Submit'}</span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#ffffff"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#ffffff"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
      </form>

      <SuccessMainModal
        heading='Next of kin details submitted successfully'
        isSuccessMainModalOpen={isSuccessModalOpen}
        setSuccessMainModalState={setSuccessModalState}
        subheading=''
      >
        <div className="flex gap-3 rounded-2xl bg-green-50 px-8 py-6">
          <LinkButton className="py-3 w-full text-sm" href='/' size="fullWidth">
            Close
          </LinkButton>
        </div>
      </SuccessMainModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-blue-100 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
