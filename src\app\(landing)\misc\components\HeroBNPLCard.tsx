import React from "react"
import { cn } from "@/utils/classNames"
import { motion } from "motion/react"

interface HeroBNPLCardProps {
    title: string | React.ReactNode
    description: string
    reverseStepping?: boolean
    images: {
        src: string
        alt: string
    }[]

}

function HeroBNPLCard({ title, description, images, reverseStepping }: HeroBNPLCardProps) {

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.5,
            },
        },
    };
    return (
        <motion.article className="bg-[#040B29] rounded-3xl p-6 lg:p-8 pb-0 flex flex-col gap-4 w-full max-w-[320px] overflow-y-hidden border-[0.3px] border-[#FFFFFF0D]" variants={itemVariants}>
            <div className="space-y-2">
                <h3 className="text-xl font-medium">{title}</h3>
                <p className="text-[#CDD5DD] text-[0.8rem] leading-relaxed text-balance">{description}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
                {
                    images.slice(0, 2).map((image, index) => (
                        <div
                            className={cn("rounded-xl overflow-hidden aspect-[9/12] translate-y-[27%]",
                                (index === 1 && !reverseStepping) && ' translate-y-[40%]',
                                (index === 0 && reverseStepping) && ' translate-y-[40%]',
                            )}
                            key={index}
                        >
                            <motion.img
                                alt={image.alt}
                                animate={{ x: 0 }}
                                className="size-full object-cover aspect-square"
                                initial={{ x: '-100%' }}
                                src={image.src}
                            />
                        </div>
                    ))
                }
            </div>
        </motion.article>
    )
}

export default HeroBNPLCard;