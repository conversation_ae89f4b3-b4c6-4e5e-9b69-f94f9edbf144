"use client"

import Link from "next/link";

import { cn } from "@/utils/classNames";

import { LinkButton } from "@/components/core";


import RightUpArrow from "@/icons/RightUpArrow";

import SeedsPennies from "@/icons/SeedsPenniesLogo";

const sections = [
    {
        title: "1. Information We Collect",
        items: [
            {
                subtitle: "Personal Information:",
                description: "When you register, we may collect details such as your name, date of birth, address, email, and phone number.",
            },
            {
                subtitle: "Financial Information:",
                description: "Information related to deposits, withdrawals, loan balances, repayments, and account top-ups.",
            },
            {
                subtitle: "Device Information:",
                description: "We may collect data related to the device you use, such as IP address, device model, and operating system.",
            },
            {
                subtitle: "Usage Data:",
                description: "Information on how you interact with Seeds, including navigation and app activity, to improve functionality and user experience.",
            },
        ],
    },
    {
        title: "2. How We Use Your Information",
        description: "We may use your data for the following purposes:",
        items: [
            {
                subtitle: "To Provide Services:",
                description: "Facilitate transactions, loan management, deposits, withdrawals, and balance updates.",
            },
            {
                subtitle: "To Improve User Experience:",
                description: "Understand app usage and optimize our services based on user behavior.",
            },
            {
                subtitle: "Security & Fraud Prevention:",
                description: "Detect and prevent fraudulent activities to protect your information and account security.",
            },
            {
                subtitle: "Customer Support:",
                description: "Address inquiries, complaints, or feedback you provide.",
            },
        ],
    },
    {
        title: "3. Sharing Your Information",
        description: "Your data will not be shared with third parties, except:",
        items: [
            {
                subtitle: "With Service Providers:",
                description: "Trusted third-party vendors assisting us with app operations, under strict confidentiality agreements.",
            },
            {
                subtitle: "For Legal Reasons:",
                description: "In response to lawful requests or to protect Seeds & Pennies’ rights and property.",
            },
        ],
    },
    {
        title: "4. Data Security",
        items: [
            {
                subtitle: "",
                description: "We prioritize the protection of your personal and financial information. Seeds uses standard encryption and secure storage protocols to safeguard your data from unauthorized access or disclosure.",
            },

        ],
    },
    {
        title: "5. User Obligations",
        items: [
            {
                subtitle: "",
                description: "Seeds is intended for individuals aged 18 and above. Users under 18 are prohibited from using the app, and any violation of this may result in termination of access. Users are responsible for securing their login credentials.",
            },

        ],
    },
    {
        title: "6. User Rights",
        description: "You have the right to:",
        items: [
            {
                subtitle: "Access:",
                description: "Request information on data we have collected about you",
            },
            {
                subtitle: "Correction:",
                description: "Update or correct your information to keep it accurate.",
            },
            {
                subtitle: "Deletion:",
                description: "Request deletion of personal data, subject to legal retention requirements.",
            },
        ],
    },
    {
        title: "7. Policy Updates",
        items: [
            {
                subtitle: "",
                description: "Seeds reserves the right to update this Privacy Policy. Significant changes will be communicated to users via email or an in-app notification.",
            },

        ],
    },
    {
        title: "8. Contact Us",
        items: [
            {
                subtitle: "",
                description: "If you have questions about this Privacy Policy, please contact us at:",
            },

            {
                subtitle: "Email:",
                description: "<EMAIL>",
            },
            {
                subtitle: "Address:",
                description: "27 Alara street sabo yaba, Lagos, Nigeria",
            },

        ],
    },
]

export default function Home() {
    return (
        <main className="  wax-w-screen max-md:pb-12">
            <section className="bg-[#040320] md:m-2 !mb-0  pb-8 rounded-3xl max-md:rounded-none md:rounded-2xl md:rounded-bl-none text-white shadow-sm">
                <header className=" flex items-center gap-4 justify-between px-8 p-6 xl:px-16">
                    <Link className={cn(" font-semibold text-xl lg:text-2xl")} href="/">
                        <SeedsPennies />
                    </Link>


                    <LinkButton className={cn("hidden md:flex items-center justify-between text-[0.865rem] text-left gap-[7px] px-5 rounded-full max-w-max", "font-display")} href="#" target="_blank" variant="white">
                        Download app
                        <span className="flex items-center justify-center rounded-full bg-main-light ">
                            <RightUpArrow className="size-[30px]" height={12} width={12} />
                        </span>
                    </LinkButton>

                </header>

                <section className="flex flex-col md:grid grid-cols-2 md:items-center sm:max-lg:px-6 md:pt-12 md:pb-6 ">
                    <div className="flex flex-col gap-4  md:gap-6 justify-self-center max-md:px-6 max-md:py-10">

                        <h1 className={cn("font-display", "flex flex-col font-semibold text-3xl md:text-4xl xl:text-5xl gap-2")}>
                            <span>
                                Privacy policy
                            </span>

                        </h1>
                        <p className="max-w-sm md:max-w-[517px] text-[0.825rem] md:text-base text-helper text-[#CAC9D4CC]">
                            Thank you for choosing Seeds, a financial app by Seeds & Pennies. This privacy policy outlines how we collect, use, and protect your information when you use Seeds. By using Seeds, you agree to the terms of this policy.
                        </p>
                    </div>
                    <LinkButton className={cn("flex md:hidden text-[0.865rem] text-left gap-[7px] px-5 rounded-full max-w-max mx-5", "font-display")} href="#" target="_blank" variant="white">
                        Download app
                        <span className="flex items-center justify-center rounded-full bg-main-light ">
                            <RightUpArrow className="size-[30px]" height={12} width={12} />
                        </span>
                    </LinkButton>


                </section>
            </section>




            <section className="flex flex-col m-2 mt-0">
                <header className="flex items-start max-md:flex-col w-full overflow-hidden">
                    {/* <div className="max-md:hidden relative bg-main w-1/2 h-12 rounded-b-2xl">
          </div> */}
                    <div className="-mt-5 max-md:hidden relative">
                        <svg className=" w-full h-12" fill="none" height="46" viewBox="0 0 812 46" width="812" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 23.25C0 10.6855 10.1855 0.5 22.75 0.5H811.066C811.229 0.5 811.361 0.632184 811.361 0.795242V0.795242C811.361 0.925141 811.304 1.04849 811.205 1.13263L766.839 38.8553C761.414 43.4675 754.526 46 747.406 46H747.268H22.75C10.1855 46 0 35.8145 0 23.25V23.25Z" fill="#040320" />
                        </svg>
                    </div>
                </header>



            </section>


            <section className="md:px-[147px]">

                <div className="p-6 bg-white text-gray-800">
                    {sections.map((section, index) => (
                        <div className="mb-8" key={index}>
                            <h2 className="text-2xl font-bold mb-4 text-black">{section.title}</h2>
                            {section.description && <p className="mb-4 text-black font-semibold pl-6">{section.description}</p>}
                            <ul className="list-disc pl-6 space-y-2">
                                {section.items.map((item, itemIndex) => (
                                    <li key={itemIndex}>
                                        <span className="font-semibold md:text-lg text-black">{item.subtitle}</span>
                                        <p className="mt-1 text-[#242424CC]">{item.description}</p>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>
            </section>


        </main>


    );
}
