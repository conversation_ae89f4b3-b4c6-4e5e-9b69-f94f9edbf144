import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface CreatePasscodeDTO {
  pin1: string;
  pin2: string;
  phone_number: string;
}

export interface CreatePasscodeResponse {
  message: string;
}

const createPasscode = (createPasscodeDTO: CreatePasscodeDTO): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/create_login_passcode/`, createPasscodeDTO);
};

export const useCreatePasscode = () => {
  return useMutation('create-passcode', createPasscode, {});
};