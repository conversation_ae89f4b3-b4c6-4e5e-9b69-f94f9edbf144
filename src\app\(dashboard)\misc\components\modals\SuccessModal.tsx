'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import {
  <PERSON>ton,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  FormError,
  Input,
} from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { addCommasToNumber } from '@/utils/numbers';
import { EnterWithdrawalPinModal } from './EnterPaymentPinModal';

interface SuccessModalProps {
  isSuccessModalOpen: boolean;
  setSuccessModalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  children?: React.ReactNode;
}

const WalletFormSchema = z.object({
  amount: z
    .string({ required_error: 'Please enter a valid amount' })
    .regex(/^\d+$/, { message: 'Please enter a valid amount' })
    .trim()
    .min(1, { message: 'Please enter a company size.' }),
});

export type WalletFormValues = z.infer<typeof WalletFormSchema>;

export function SuccessModal({
  isSuccessModalOpen,
  setSuccessModalState,
  heading,

}: SuccessModalProps) {


  const {
    state: isCreateWithdrawalPinModalOpen,
    setState: setCreateWithdrawalPinModalState,
    setTrue: openCreateWithdrawalPinModal,
  } = useBooleanStateControl();

  const [bankAmount, setBankAmount] = React.useState('');

  const {
    handleSubmit,
    register,
    control,
    formState: { errors },
  } = useForm<WalletFormValues>({
    resolver: zodResolver(WalletFormSchema),
    defaultValues: {
      amount: undefined,
    },
  });

  const amount = useWatch({
    control,
    name: 'amount',
  });

  const onWithdrawalSubmit = (submittedData: WalletFormValues) => {
    setBankAmount(submittedData.amount)
    openCreateWithdrawalPinModal()
  };

  return (
    <Dialog open={isSuccessModalOpen} onOpenChange={setSuccessModalState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="p-0 text-left">
          <div className="px-8 py-6">
            <p className="mt-1 text-[13px] text-[#6C727F]">
              Fund wallet with any of the underlisted options
            </p>
            <form
              className="mt-[30px] w-full space-y-8"
              onSubmit={handleSubmit(onWithdrawalSubmit)}
            >
              <Input
                autoCapitalize="none"
                autoComplete="off"
                autoCorrect="off"
                id="amount"
                placeholder="Enter amount"
                type="number"
                {...register('amount')}
              />

              {errors?.amount && (
                <FormError errorMessage={errors.amount.message} />
              )}

              <Button
                className="font-sora mt-8 w-full rounded-lg bg-[#032282] px-4 py-[10px] text-right text-sm font-normal text-white"
              // onClick={openSavedBenneficiariesModal}
              >
                <div className="flex w-full items-center justify-end gap-[18px] rounded-[5px]">
                  Withdraw
                  <span className="bg-[#062996] px-[27px] py-2 text-xs text-white">
                    ₦{addCommasToNumber(amount as unknown as number) || '00.00'}
                  </span>
                </div>
              </Button>
            </form>
          </div>
        </DialogBody>
      </DialogContent>


      <EnterWithdrawalPinModal
        bankAmount={bankAmount}
        heading="Transaction Pin"
        isPinModalOpen={isCreateWithdrawalPinModalOpen}
        setPinModalState={setCreateWithdrawalPinModalState}
      />
    </Dialog>
  );
}
