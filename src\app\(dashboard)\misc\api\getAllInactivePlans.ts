import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface AllInactivePlansData {
  status: boolean
  count: number
  next: string
  previous: string
  results: AllInactivePlansEntity[]
}

export interface AllInactivePlansEntity {
  id: number
  ajo_user: string
  user: User
  name: string
  onlending_type: string
  duration: number
  target: number
  interest_rate: number
  periodic_amount: number
  frequency: string
  hour: string
  maturity_date: string
  estimated_amount: number
  quotation_id: string
  amount_saved: number
  interest_type: string
  is_activated: boolean
  completed: boolean
  is_active: boolean
  interest_paid: boolean
}

export interface User {
  email: string
}

export const getAllInactivePlans = async (email: string): Promise<AllInactivePlansData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get(`/onlending/plans?filter=${"inactive"}`, {headers});
  return data;
};

export const useAllInactivePlans = (email: string) =>
  useQuery('all-lending-inactive-plans', () => getAllInactivePlans(email), );
 