'use client'

import { motion } from "framer-motion"
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/core/Accordion"
import Arrow from "@/icons/Arrow";
import { LinkButton } from "@/components/core";
import { cn } from "@/utils/classNames";

const faqs = [
    {
        question: "How do i access a loan",
        answer: "To access the seeds and pennies loan, all you are required to do it visit one of our branches nearest to you to get in church with one of our loan officers"
    },
    {
        question: "What equipment is available at the studio?",
        answer: "We have a comprehensive range of professional recording equipment including industry-standard microphones, pre-amps, interfaces, and monitoring systems. Contact us for a detailed equipment list."
    },
    {
        question: "Do you provide engineers or do I need to bring my own?",
        answer: "We provide experienced engineers for all sessions. However, you're welcome to bring your own engineer if you prefer."
    },
    {
        question: "Can I tour the studio before booking?",
        answer: "Yes, we offer studio tours by appointment. Contact us to schedule a convenient time."
    },
    {
        question: "Do you offer discounts for long-term projects?",
        answer: "Yes, we offer special rates for long-term bookings and full album projects. Contact us to discuss your project needs."
    },

]

export default function FAQSection() {
    return (
        <section className="flex flex-col justify-center bg-[#000619] text-white py-20 min-h-[85vh]">
            <div className="container mx-auto">
            </div>
            <div className="w-[95%] md:w-[85%] max-w-[1450px] mx-auto lg:flex items-start gap-16 xl:gap-24 xl:justify-between">
                <div className="max-md:pl-5">
                    <p className="text-white/70 md:text-">
                        Frequently Asked
                        Questions
                    </p>
                    <motion.h2
                        className="text-4xl md:text-6xl font-medium mb-8 mt-4  text-white text-balance max-w-[50ch]"
                        initial={{ opacity: 0.2, y: 20 }}
                        transition={{ duration: 0.5 }}
                        whileInView={{ opacity: 1, y: 0 }}
                    >
                        Frequently Asked
                        Questions
                    </motion.h2>
                    <LinkButton className={cn("hidden md:flex items-center justify-between text-[0.865rem] text-left gap-[7px] px-5 max-w-max", "font-display")} href="/get-started" size="lg" >
                        Get Started
                        <Arrow className="size-[20px] ml-5 rotate-[-135deg]" />
                    </LinkButton>
                </div>

                <Accordion className="space-y-3 w-full max-w-[750px]" type="single" collapsible>
                    {faqs.map((faq, index) =>
                        <AccordionItem className=" !bg-[#FFFFFF0D] px-6 rounded-lg !border-none" key={index} value={`item-${index}`}>
                            <AccordionTrigger className="text-left text-lg font-sans font-medium hover:text-white/90 hover:!no-underline" plus_minus_icon>
                                {faq.question}
                            </AccordionTrigger>
                            <AccordionContent className="text-[#FFFFFFB2] font-sans md:max-w-[620px] text-balance text-sm md:text-base max-md:font-light leading-loose">
                                {faq.answer}
                            </AccordionContent>
                        </AccordionItem>
                    )}
                </Accordion>
            </div>
        </section>
    )
}