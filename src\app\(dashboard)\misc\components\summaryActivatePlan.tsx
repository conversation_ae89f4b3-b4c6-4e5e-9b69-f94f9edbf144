'use client';

import React from 'react';

import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useBooleanStateControl } from '@/hooks';


import { SaveIntrestsOptions } from './modals/saveIntrestsOptions';
import { Button, Switch } from '@/components/core';
import { addCommasToNumber } from '@/utils';
import { formatDateStructure } from '@/utils/formatShortDate';



export function SummaryActivatePlan() {

    const { emailAddress, createPlanResponse } = useOnboardingPlan();

    const {
        state: isSaveIntrestOptionsOpen,
        setState: setSaveIntrestOptionsState,
        setTrue: openSaveIntrestOptions,
    } = useBooleanStateControl();

    const [enabled, setEnabled] = React.useState(true);

    const initiateCreatePlan = () => {
        openSaveIntrestOptions()
    }

    return (
        <>

            <div className='mb-10'>
                <p className='text-[#242424] text-xl font-semibold'>Onlending plan summary</p>
            </div>

            <div className='space-y-4'>
                <div className='flex justify-between items-center border-b-[0.5px] border-b-[#E9E9E9] pb-4 '>
                    <p className='text-[#56566A] text-sm'>Interest rate</p>
                    <p className='text-[#080808] text-sm font-semibold'>3.5%</p>
                </div>
                <div className='flex justify-between items-center border-b-[0.5px] border-b-[#E9E9E9] pb-4 '>
                    <p className='text-[#56566A] text-sm'>Amount </p>
                    <p className='text-[#080808] text-sm font-semibold'>₦{addCommasToNumber(Number(createPlanResponse?.target)) || 0}</p>
                </div>
                <div className='flex justify-between items-center border-b-[0.5px] border-b-[#E9E9E9] pb-4 '>
                    <p className='text-[#56566A] text-sm'>Interest option</p>
                    <p className='text-[#080808] text-sm font-semibold'>{createPlanResponse?.interest_type || 0}</p>
                </div>
                <div className='flex justify-between items-center border-b-[0.5px] border-b-[#E9E9E9] pb-4 '>
                    <p className='text-[#56566A] text-sm'>Maturity date</p>
                    <p className='text-[#080808] text-sm font-semibold'>{createPlanResponse?.maturity_date || 0}</p>
                </div>
                <div className='flex justify-between items-center border-b-[0.5px] border-b-[#E9E9E9] pb-4 '>
                    <p className='text-[#56566A] text-sm'>Interest amount</p>
                    <p className='text-[#080808] text-sm font-semibold'>3.5%</p>
                </div>
                <div className='flex justify-between items-center border-b-[0.5px] border-b-[#E9E9E9] pb-4 '>
                    <p className='text-[#56566A] text-sm'>Duration</p>
                    <p className='text-[#080808] text-sm font-semibold'>{createPlanResponse?.duration || 0}days</p>
                </div>
            </div>

            <div className='mt-20'>
                <div className='w-full bg-[#F5FAFF] rounded-lg py-2 px-6'>
                    <div className="my-6 flex items-start gap-[10px]">
                        <Switch
                            checked={enabled}
                            className=""
                            id="airplane-mode"
                            onChange={() => setEnabled(!enabled)} // Toggle the enabled state
                        />


                        <p className='text-sm font-normal text-[#646464] text-justify'>
                            I hereby authorize Seeds to create an onlending plan of <span className='text-[#032282] font-semibold'>₦{addCommasToNumber(Number(createPlanResponse?.target)) || 0}</span> and ensure its availability at the designated maturity date of <span className='text-[#032282] font-semibold'>{formatDateStructure(String(createPlanResponse?.maturity_date)) || 0}</span>. Furthermore, any premature termination of this onlending arrangement will lead to a reduction in the accrued interest.
                        </p>
                    </div>
                </div>
            </div>

            <div className='bg-white py-[14px] mt-4 px-6 flex items-center gap-[23px]'>
                <Button className='py-4' size="fullWidth" onClick={initiateCreatePlan}>Activate plan</Button>
                <Button className='py-4 bg-[#F5FAFF] text-[#032282] border-[#032282] border-[0.5px] ' size="fullWidth" onClick={initiateCreatePlan}>Edit plan</Button>
            </div>

            <SaveIntrestsOptions
                email={String(emailAddress)}
                heading='Save'
                isSaveIntrestsOptionsOpen={isSaveIntrestOptionsOpen}
                setSaveIntrestsOptionsState={setSaveIntrestOptionsState}
            />

        </>
    );
}
