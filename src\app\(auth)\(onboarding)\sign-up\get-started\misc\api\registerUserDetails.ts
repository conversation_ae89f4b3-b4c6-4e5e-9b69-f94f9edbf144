// import { adminLoanAxios } from '@/lib/axios';

// import { useMutation } from '@tanstack/react-query';

// export interface RegisterUserDetailsDTO {
//   phone_number: string;
//   username?: string;
//   first_name: string;
//   last_name: string;
//   email: string;
//   state: string;
//   lga: string;
//   nearest_landmark: string;
//   street: string;
//   type_of_user: string;
//   referal_code?: string;
//   business_name?: string;
//   gender: string;
//   source: string;
// }

// export interface RegisterUserDetailsResponse {
//   message: string;
//   passcode: string;
// }

// const registerUserDetails = async (
//   registerUserDetailsDTO: RegisterUserDetailsDTO
// ) => {
//   const response = await adminLoanAxios.post(
//     '/agency/user/create_user_detail/',
//     registerUserDetailsDTO
//   );
//   return response.data as RegisterUserDetailsResponse;
// };

// export const useRegisterUserDetails = () => {
//   return useMutation({
//     mutationFn: registerUserDetails,
//   });
// };

import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';

export interface RegisterUserDetailsDto {
  phone_number: string;
  username?: string;
  first_name: string;
  last_name: string;
  email: string;
  state: string;
  lga: string;
  nearest_landmark: string;
  street: string;
  type_of_user: string;
  referal_code?: string;
  business_name?: string;
  gender: string;
  source: string;
}

const registerUserDetails = (registerUserDetailsDto:RegisterUserDetailsDto): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/create_user_detail/`, registerUserDetailsDto);
};

export const useRegisterUserDetails = () => {
  return useMutation('registerUserDetails', registerUserDetails, {});
};


