"use client"

import { motion } from "framer-motion"
import { Card } from "@/components/core"
import { cn } from "@/utils/classNames"
import Image from "next/image"




interface TestimonialCardProps {
    name: string
    businessType: string
    quote: string
    imageSrc?: string
    className?: string
    delay?: number
    area?: string
}

export function TestimonialCard({
    name,
    businessType,
    quote,
    imageSrc,
    className = "",
    delay = 0
}: TestimonialCardProps) {
    return (
        <motion.div
            className={cn(className, "rounded-2xl")}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay }}
            whileInView={{ opacity: 1, y: 0 }}
        >
            <Card className={cn("h-full bg-[#0E1325] border-none overflow-hidden rounded-[2.25rem]", imageSrc && "grid md:grid-cols-[1fr,0.9fr]")}>
                <div className="p-6 md:py-12 md:px-10">
                    <h3 className="text-2xl md:text-3xl font-semibold text-white mb-1">
                        {name}
                    </h3>
                    <p className="text-gray-400 mb-6">{businessType}</p>
                    <blockquote className="relative">
                        <span className="absolute -left-2 top-0 text-3xl text-white font-semibold md:text-4xl italic">&quot;</span>
                        <p className="text-[#FFFFFFB2] text-lg leading-relaxed pl-6 text-balance font-light">
                            {quote}
                        </p>
                        <span className="absolute -bottom-4 right-0 text-3xl text-white font-semibold md:text-4xl italic">&quot;</span>
                    </blockquote>
                </div>
                {imageSrc && (
                    <section className="p-4 md:p-6 ">

                        <div className="relative size-full max-md:h-60 rounded-[2rem] overflow-hidden">
                            <Image
                                alt=""
                                className="w-full h-48 object-cover rounded-xl"
                                src={imageSrc}
                                fill
                            />
                        </div>
                    </section>
                )}
            </Card>
        </motion.div>
    )
}



const testimonials = [
    {
        name: "Chukwuma",
        businessType: "Business man",
        quote: "The Seeds asset financing option was a lifesaver for my frozen food business when my smaller freezer broke down unexpectedly. It allowed me to secure a large chest freezer and pay for it with ease, ensuring my business stayed on track.",
        imageSrc: "/images/landing/CUSTOMERS_SAY_1.png",
        area: "md:[grid-area:main1]"
    },
    {
        name: "Bukola idris",
        businessType: "Business woman",
        quote: "The seeds loan have really impacted my business positively as they have been my business financial back bone, and have always come through for my business financially with instant loan all the times i have come calling.",
        area: "md:[grid-area:side1]"
    },
    {
        name: "Bilkisu ahmed",
        businessType: "Business woman",
        quote: "The seeds loan have really impacted my business positively as they have been my business financial back bone, and have always come through for my business financially with instant loan all the times i have come calling.",
        area: "md:[grid-area:side2]"
    },
    {
        name: "Oladele Joshua",
        businessType: "Business man",
        quote: "The Seeds asset financing option was a lifesaver for my frozen food business when my smaller freezer broke down unexpectedly. It allowed me to secure a large chest freezer and pay for it with ease, ensuring my business stayed on track.",
        imageSrc: "/images/landing/CUSTOMERS_SAY_2.jpg",
        area: "md:[grid-area:main2]"
    }
]

export default function CustomersSaySection() {
    return (
        <section className="bg-black px-4 md:px-8 pt-24 pb-48 max-md:pb-44">
            <div className="max-w-7xl mx-auto">
                <div className="text-left mb-16">
                    <motion.p
                        className="text-[#CDD5DD] text-xl mb-4"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                    >
                        Testimonials
                    </motion.p>
                    <motion.h2
                        className="text-4xl md:text-6xl font-medium text-white mb-4"
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ delay: 0.2 }}
                        whileInView={{ opacity: 1, y: 0 }}
                    >
                        What our customers say
                    </motion.h2>
                </div>

                <div
                    className="grid gap-6 lg:gap-8 grid-cols-1 md:grid-cols-3 md:grid-rows-[auto_auto] md:[grid-template-areas:'main1_main1_side1''side2_main2_main2']"
                >
                    {testimonials.map((testimonial, index) => (
                        <TestimonialCard
                            className={`${testimonial.area}`}
                            delay={0.2 + index * 0.1}
                            key={testimonial.name}
                            {...testimonial}
                        />
                    ))}
                </div>
            </div>
        </section>
    )
}

