import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';


export interface EnrollmentLinkResponse {
  status: boolean
  enrollment_link: string
}



export const getEnrollmentLink = async (email: string): Promise<EnrollmentLinkResponse> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
   
  const { data } = await savingsAxios.get(`/onlending/enrollment-link/`, {headers});
  return data;
};

export const useEnrollmentLink = (email: string) =>
  useQuery('enrollment-link', () => getEnrollmentLink(email), );
 