import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface DmoSummaryData {
  status: boolean
  data: DmoSummaryEntity
}

export interface DmoSummaryEntity {
  main_wallet_balance: number
  total_balance: number
  total_savings: number
  commissions_balance: string
  referrals: string
}





export const getDmoSummary = async (email: string): Promise<DmoSummaryData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
   
  const { data } = await savingsAxios.get(`/onlending/summary/`, {headers});
  return data;
};

export const useDmoSummary = (email: string) =>
  useQuery('dmo-summary', () => getDmoSummary(email), );
 