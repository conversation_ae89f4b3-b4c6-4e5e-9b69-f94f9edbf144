import React from 'react';
import { useEffect, useCallback, useState } from 'react'
import { PauseIcon, PlayIcon } from 'lucide-react';
import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import Link from 'next/link';
import AutoScroll from 'embla-carousel-auto-scroll';

import { cn } from '@/utils/classNames';
import { Carets } from '@/icons/core';


interface TCarouselItem {
    title: string;
    subtitle: string;
    text_color: string;
    bg_color: string;
    image: string;
    alt: string;
    image_container_class?: string
}


interface FeauturesSlideShowProps {
    slides: TCarouselItem[]
}
const FeauturesSlideShow: React.FC<FeauturesSlideShowProps> = ({ slides }) => {
    const options: EmblaOptionsType = { loop: true }

    const [emblaRef, emblaApi] = useEmblaCarousel(options, [
        AutoScroll({ playOnInit: true, speed: 3 })
    ])
    const [isPlaying, setIsPlaying] = useState(true)



    // const onButtonAutoplayClick = useCallback(
    //     (callback: () => void) => {
    //         const autoScroll = emblaApi?.plugins()?.autoScroll
    //         if (!autoScroll) return

    //         const resetOrStop =
    //             autoScroll.options.stopOnInteraction === false
    //                 ? autoScroll.reset
    //                 : autoScroll.stop

    //         resetOrStop()
    //         callback()
    //     },
    //     [emblaApi]
    // )

    const toggleAutoplay = useCallback(() => {
        const autoScroll = emblaApi?.plugins()?.autoScroll
        if (!autoScroll) return

        const playOrStop = autoScroll.isPlaying()
            ? autoScroll.stop
            : autoScroll.play
        playOrStop()
    }, [emblaApi])

    useEffect(() => {
        const autoScroll = emblaApi?.plugins()?.autoScroll
        if (!autoScroll) return

        setIsPlaying(autoScroll.isPlaying())
        emblaApi
            .on('autoScroll:play', () => setIsPlaying(true))
            .on('autoScroll:stop', () => setIsPlaying(false))
            .on('reInit', () => setIsPlaying(autoScroll.isPlaying()))
    }, [emblaApi])

    return (
        <div
            className="embla w-full m-auto max-md:[--slide-size:85%] md:[--slide-size:40%] lg:[--slide-size:30%] 2xl:[--slide-size:450px]"
            style={{
                '--slide-height': '19rem',
                '--slide-spacing': '0.3rem',
                // '--slide-size': '25%',
            } as React.CSSProperties}
        >
            <div className="relative overflow-hidden" ref={emblaRef}>
                <div className="relative flex [touch-action:pan-y_pinch-zoom] [margin-left:calc(var(--slide-spacing)_*_-1)]"
                // onMouseOver={toggleAutoplay}
                // onMouseOut={toggleAutoplay}
                >
                    {[...slides, ...slides].map(({ title, subtitle, bg_color, image, image_container_class, text_color }, index) => (
                        <div
                            className={cn("flex flex-col h-[48vh] min-h-[300px] lg:min-h-[400px] lg:max-h-[480px] apsect-[4/5] ",
                                "[flex:0_0_var(--slide-size)] ml-[var(--slide-spacing)] [transform:translate3d(0,0,0)]"
                            )}
                            key={index}
                            style={{
                                backgroundColor: bg_color
                            }}
                        >
                            <h2 className='p-4 md:p-8 pb-0 md:pb-0 text-2xl font-medium md:text-4xl text-balance min-h-[2lh]' style={{ color: text_color }}>
                                {title}
                            </h2>
                            <p className='p-4 md:pl-8 font-light text-balance' style={{ color: text_color }}>
                                {subtitle}
                            </p>
                            <div className={cn(
                                'relative mt-auto', image_container_class
                            )}>
                                <img
                                    alt={`Slider image ${index + 1}`}
                                    className="w-full h-full object-contain text-[0.675rem]"
                                    src={image}
                                />
                            </div>
                        </div>
                    ))}
                </div>
                <div className="absolute bottom-0 right-0 p-5">
                    <button className="p-4 rounded-full bg-black/50 text-white" type="button" onClick={toggleAutoplay} >
                        {isPlaying ? <PauseIcon /> : <PlayIcon />}
                    </button>
                </div>
            </div>

        </div>
    )
}





const LOAN_PRODUCTS = [
    {
        title: "Buy Now Pay Later",
        subtitle: "Get items of your choice and 'pay small small'",
        text_color: "#5A0027",
        bg_color: "#E7C7DC80",
        image: "/images/landing/FEATURES_LOAN_1.png", alt: "BNPL Phones",
        image_container_class: "md:px-8 w-[90%] mx-auto"
    },
    {
        title: "Instant loans",
        subtitle: "Make contributions and get instant loan disbursement",
        text_color: "#1255C3",
        bg_color: "#EBF1FF",
        image: "/images/landing/FEATURES_LOAN_2.png", alt: "Loan Features",
        image_container_class: "md:pr-8"
    },
    {
        title: "Savings backed loans",
        subtitle: "Unlock a loan of up to 5x your savings, and still have your saving intact",
        text_color: "#021873",
        bg_color: "#F1F8FF",
        image: "/images/landing/FEATURES_LOAN_3.png", alt: "Saving, Pig Bank, Coins",
        image_container_class: "md:pr-8 lg:pr-10 pl-4"
    },
    
]
const SAVING_PRODUCTS = [
    {
        title: "Ajo savings",
        subtitle: "Get items of your choice and 'pay small small'",
        text_color: "#009948",
        bg_color: "#C2E6D3",
        image: "/images/landing/FEATURES_SAVINGS_1.png", alt: "BNPL Phones",
        image_container_class: "md:px-8 w-[85%] mx-auto"
    },
    {
        title: "Group savings",
        subtitle: "Make contributions and get instant loan disbursement",
        text_color: "#E78D07",
        bg_color: "#FFF3EA",
        image: "/images/landing/FEATURES_SAVINGS_2.png", alt: "Loan Features",
        image_container_class: "md:px-8 w-[85%] mx-auto"
    },
    {
        title: "Onlending",
        subtitle: "Seeds Onlending offers you the opportunity to lend money to borrowers with low risk while earning a competitive annual interest of 20%.",
        text_color: "#021873",
        bg_color: "#F1F8FF",
        image: "/images/landing/FEATURES_SAVINGS_3.png", alt: "Liberty Tech",
        image_container_class: "md:px-8 w-[85%] mx-auto"
    },

]


const FeauturesSlider: React.FC = () => {
    const [feature, setFeature] = useState('loan')
    return (
        <section className=" py-12 md:py-16">
            <header className='flex items-center justify-between pb-3 md:mb-6 px-2.5 md:px-12'>
                <div className="grid grid-cols-2 gap-1.5 p-1.5 rounded-lg bg-[#F1F8FF]">
                    <button className={cn(
                        'text-[#1255C3] rounded-xl max-md:text-xs px-4 md:px-10 py-2.5 transition-colors hover:bg-[#1255C3]/20',
                        feature == "loan" && '!bg-[#1255C3] text-white',
                    )}
                        onClick={() => setFeature('loan')}
                    >
                        Loan products
                    </button>
                    <button className={cn(
                        'text-[#1255C3] rounded-xl max-md:text-xs px-4 md:px-10 py-2.5 transition-colors hover:bg-[#1255C3]/20',
                        feature == "savings" && '!bg-[#1255C3] text-white',
                    )}
                        onClick={() => setFeature('savings')}
                    >
                        Savings products
                    </button>
                </div>

                <Link className='flex items-center gap-4 text-sm py-2 bg-[#F1F8FF] hover:bg-[#e7f1fc] text-[#032282] px-5 max-md:!p-1 rounded-lg transition-colors duration-300' href="/get-started" >
                    <span className='max-md:hidden'>
                        Get loan
                    </span>
                    <Carets className='ml-4' />
                </Link>
            </header>
            {
                feature == 'loan' ?
                    <FeauturesSlideShow slides={LOAN_PRODUCTS} />
                    :
                    <FeauturesSlideShow slides={SAVING_PRODUCTS} />
            }
        </section>
    );
};

export default FeauturesSlider;


