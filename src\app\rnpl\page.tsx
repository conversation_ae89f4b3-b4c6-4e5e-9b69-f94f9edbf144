'use client'

import * as React from 'react';
import { OnboardingPageWrapper } from '../(auth)/(onboarding)/misc';
import { Button, ErrorModal, Input, RadioGroup, RadioGroupItem, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core';
import { Label } from '@radix-ui/react-label';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { cn } from '@/utils/classNames';
import { usePostRnplOTP } from './misc/api/postRnpl';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/core/Dialog';
import { CheckCircle, Phone, MessageCircle, Shield } from 'lucide-react';
import { useErrorModalState } from '@/hooks';
import { formatNumberWithCommas } from '@/utils/formatNumber';

const formSchema = z.object({
    full_name: z.string().min(2, 'Full name is required'),
    phone_number: z.string().min(10, 'Valid phone number is required'),
    email: z.string().email('Valid email is required'),
    location: z.string().min(2, 'Location is required'),
    employment_status: z.enum(['yes', 'no'], { required_error: 'Please select employment status' }),
    monthly_income: z.string({ required_error: 'Please select income range' }),
    loan_purpose: z.enum(['new', 'renewal'], { required_error: 'Please select loan purpose' }),
    annual_rent: z.string().min(1, 'Annual rent amount is required'),
    repayment_duration: z.enum(['6', '9', '12'], { required_error: 'Please select repayment duration' }),
    has_valid_id: z.enum(['yes', 'no'], { required_error: 'Please select ID status' }),
    willing_to_provide_docs: z.enum(['yes', 'no'], { required_error: 'Please select document provision status' })
});

type FormData = z.infer<typeof formSchema>;

export default function RNPLApplication() {
    const [showSuccessModal, setShowSuccessModal] = React.useState(false);

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
        reset
    } = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            full_name: '',
            phone_number: '',
            email: '',
            location: '',
            employment_status: undefined,
            monthly_income: '',
            loan_purpose: undefined,
            annual_rent: '',
            repayment_duration: undefined,
            has_valid_id: undefined,
            willing_to_provide_docs: undefined
        }
    });

    const { mutate: verifyRnplForm, isLoading: isVerifyRnplFormLoading } =
        usePostRnplOTP();

    const onSubmit = (data: FormData) => {
        // Convert annual_rent from formatted string to number
        const formattedData = {
            ...data,
            annual_rent: parseInt(data.annual_rent.replace(/,/g, ''), 10)
        };

        verifyRnplForm(
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            formattedData,
            {
                onSuccess: () => {
                    setShowSuccessModal(true);
                    reset();
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage as string);
                },
            }
        );
    };

    return (
        <OnboardingPageWrapper
            heading="Rent Now, Pay Later Application"
            subHeading="Fill out the form below to apply for flexible rent payment options."
        >
            <div className='relative'>
                <form className="space-y-6 p-2" onSubmit={handleSubmit(onSubmit)}>
                    {/* Basic Info Section */}
                    <div className="space-y-4">
                        <h2 className="text-xl font-semibold text-white">Basic Info</h2>

                        <div className="space-y-2">
                            <Label className='text-white text-sm' htmlFor="full_name">Full Name</Label>
                            <Input
                                id="full_name"
                                type="text"
                                {...register('full_name')}
                                className={cn(errors.full_name && 'border-red-500')}
                            />
                            {errors.full_name && (
                                <p className="text-red-500 text-sm">{errors.full_name.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm' htmlFor="phone_number">Phone Number</Label>
                            <Input
                                id="phone_number"
                                type="tel"
                                {...register('phone_number')}
                                className={cn(errors.phone_number && 'border-red-500')}
                            />
                            {errors.phone_number && (
                                <p className="text-red-500 text-sm">{errors.phone_number.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm' htmlFor="email">Email Address</Label>
                            <Input
                                id="email"
                                type="email"
                                {...register('email')}
                                className={cn(errors.email && 'border-red-500')}
                            />
                            {errors.email && (
                                <p className="text-red-500 text-sm">{errors.email.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm' htmlFor="location">Location (City/State)</Label>
                            <Input
                                id="location"
                                type="text"
                                {...register('location')}
                                className={cn(errors.location && 'border-red-500')}
                            />
                            {errors.location && (
                                <p className="text-red-500 text-sm">{errors.location.message}</p>
                            )}
                        </div>
                    </div>

                    {/* Prequalification Section */}
                    <div className="space-y-4">
                        <h2 className="text-xl font-semibold text-white">Prequalification</h2>

                        <div className="space-y-2">
                            <Label className='text-white text-sm'>Are you currently employed or running a business?</Label>
                            <RadioGroup
                                className="flex gap-4"
                                value={watch('employment_status')}
                                onValueChange={(value) => setValue('employment_status', value as 'yes' | 'no')}
                            >
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="employed-yes"
                                        value="yes"
                                    />
                                    <Label className='text-white text-sm' htmlFor="employed-yes">Yes</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="employed-no"
                                        value="no"
                                    />
                                    <Label className='text-white text-sm' htmlFor="employed-no">No</Label>
                                </div>
                            </RadioGroup>
                            {errors.employment_status && (
                                <p className="text-red-500 text-sm">{errors.employment_status.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm'>What is your average monthly income?</Label>
                            <Select
                                value={watch('monthly_income')}
                                onValueChange={(value) => setValue('monthly_income', value)}
                            >
                                <SelectTrigger className={cn(errors.monthly_income && 'border-red-500')}>
                                    <SelectValue placeholder="Select income range" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="150000-299999">₦150,000 – ₦299,999</SelectItem>
                                    <SelectItem value="300000-499999">₦300,000 - ₦499,999</SelectItem>
                                    <SelectItem value="500000+">₦500,000 and above</SelectItem>
                                </SelectContent>
                            </Select>
                            {errors.monthly_income && (
                                <p className="text-red-500 text-sm">{errors.monthly_income.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm'>Do you need this loan to pay for a new rent or to renew an existing one?</Label>
                            <RadioGroup
                                className="flex gap-4"
                                value={watch('loan_purpose')}
                                onValueChange={(value) => setValue('loan_purpose', value as 'new' | 'renewal')}
                            >
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="purpose-new"
                                        value="new"
                                    />
                                    <Label className='text-white text-sm' htmlFor="purpose-new">New rent</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="purpose-renewal"
                                        value="renewal"
                                    />
                                    <Label className='text-white text-sm' htmlFor="purpose-renewal">Renewal</Label>
                                </div>
                            </RadioGroup>
                            {errors.loan_purpose && (
                                <p className="text-red-500 text-sm">{errors.loan_purpose.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm' htmlFor="annual_rent">How much is your total annual rent? <span className='text-xs'>(For new rent, agreement and commission inclusive)</span></Label>
                            <Input
                                id="annual_rent"
                                placeholder="e.g., ₦400,000"
                                type="text"
                                {...register('annual_rent', {
                                    onChange: (e) => {
                                        // Remove any non-digit characters
                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                        // Format the number with commas
                                        const formattedValue = formatNumberWithCommas(value);
                                        // Update the input value
                                        e.target.value = formattedValue;
                                    }
                                })}
                                className={cn(errors.annual_rent && 'border-red-500')}
                            />
                            {errors.annual_rent && (
                                <p className="text-red-500 text-sm">{errors.annual_rent.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm'>Preferred repayment duration</Label>
                            <RadioGroup
                                className="flex gap-4"
                                value={watch('repayment_duration')}
                                onValueChange={(value) => setValue('repayment_duration', value as '6' | '9' | '12')}
                            >
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="duration-6"
                                        value="6"
                                    />
                                    <Label className='text-white text-sm' htmlFor="duration-6">6 months</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="duration-9"
                                        value="9"
                                    />
                                    <Label className='text-white text-sm' htmlFor="duration-9">9 months</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="duration-12"
                                        value="12"
                                    />
                                    <Label className='text-white text-sm' htmlFor="duration-12">12 months</Label>
                                </div>
                            </RadioGroup>
                            {errors.repayment_duration && (
                                <p className="text-red-500 text-sm">{errors.repayment_duration.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm'>Do you have a valid means of ID and BVN?</Label>
                            <RadioGroup
                                className="flex gap-4"
                                value={watch('has_valid_id')}
                                onValueChange={(value) => setValue('has_valid_id', value as 'yes' | 'no')}
                            >
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="id-yes"
                                        value="yes"
                                    />
                                    <Label className='text-white text-sm' htmlFor="id-yes">Yes</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="id-no"
                                        value="no"
                                    />
                                    <Label className='text-white text-sm' htmlFor="id-no">No</Label>
                                </div>
                            </RadioGroup>
                            {errors.has_valid_id && (
                                <p className="text-red-500 text-sm">{errors.has_valid_id.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label className='text-white text-sm'>Are you willing to provide necessary documents if required?</Label>
                            <RadioGroup
                                className="flex gap-4"
                                value={watch('willing_to_provide_docs')}
                                onValueChange={(value) => setValue('willing_to_provide_docs', value as 'yes' | 'no')}
                            >
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="docs-yes"
                                        value="yes"
                                    />
                                    <Label className='text-white text-sm' htmlFor="docs-yes">Yes</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                        className="border-white data-[state=checked]:bg-white data-[state=checked]:text-[#032282]"
                                        id="docs-no"
                                        value="no"
                                    />
                                    <Label className='text-white text-sm' htmlFor="docs-no">No</Label>
                                </div>
                            </RadioGroup>
                            {errors.willing_to_provide_docs && (
                                <p className="text-red-500 text-sm">{errors.willing_to_provide_docs.message}</p>
                            )}
                        </div>
                    </div>

                    {/* <button
                        className="w-full bg-[#032282] text-white py-3 px-4 rounded-md hover:bg-[#032282]/90 transition-colors"
                        type="submit"
                    >

                    </button> */}

                    <Button
                        className="mt-9 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal] transition-colors"
                        disabled={isVerifyRnplFormLoading}
                        type="submit"
                        variant="white"
                    >
                        <span className="flex w-full items-center justify-between">
                            <span />

                            <span>{isVerifyRnplFormLoading ? 'Loading' : 'Submit Application'}</span>

                            <svg
                                fill="none"
                                height={20}
                                viewBox="0 0 25 20"
                                width={25}
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    clipRule="evenodd"
                                    d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                                    fill="#032180"
                                    fillRule="evenodd"
                                />
                                <path
                                    clipRule="evenodd"
                                    d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                                    fill="#032180"
                                    fillRule="evenodd"
                                    opacity={0.3}
                                />
                            </svg>
                        </span>
                    </Button>

                </form>
            </div>

            <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
                <DialogContent className="sm:max-w-[480px] bg-white border-0 shadow-2xl overflow-auto">
                    {/* Decorative background */}
                    <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#032282] via-blue-500 to-green-500"></div>

                    <DialogHeader className="text-center pt-6 pb-2">


                        <DialogTitle className="text-2xl font-bold text-[#032282] mb-2">Application Received!</DialogTitle>

                    </DialogHeader>

                    <div className="px-6 pb-6 space-y-6">
                        {/* Success Icon with animation */}
                        <div className='flex items-center gap-3 mt-3'>
                            <div className="size-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                                <CheckCircle className="size-8 text-white" />

                            </div>
                            <p className="text-gray-600 text-base">Thank you for taking the first step with us</p>

                        </div>
                        {/* Main message */}
                        <div className="text-center">
                            <p className="text-gray-700 text-sm leading-relaxed">
                                Our dedicated team will contact you within{" "}
                                <span className="font-semibold text-[#032282]">24 hours</span> to guide you through the complete
                                application process.
                            </p>
                        </div>

                        {/* Contact information card */}
                        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-5 rounded-xl border border-blue-100">
                            <div className="flex items-center gap-2 mb-3">
                                <div className="size-8 bg-[#032282] rounded-full flex items-center justify-center">
                                    <Phone className="size-4 text-white" />
                                </div>
                                <p className="text-[#032282] font-semibold text-sm">Need immediate help?</p>
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-start gap-2">
                                <Phone className="size-4 text-gray-500" />
                              
                                    <div>
                                        <p className="text-gray-700 text-sm">
                                            Call us:
                                        </p>
                                        <p>
                                            <span className="text-[#032282] font-semibold text-xs whitespace-nowrap">+2349024675224, +2348090643057</span>
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-2">
                                <MessageCircle className="size-4 text-gray-500" />
                                    <div>
                                        <p className="text-gray-700 text-sm">
                                        WhatsApp:
                                        </p>
                                        <p>
                                            <span className="text-[#032282] font-semibold text-xs whitespace-nowrap">+2349024675224, +2348090643057</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>


                        {/* Action buttons */}
                        <div className="flex gap-3 pt-2">
                            <Button
                                className="flex-1 bg-[#032282] hover:bg-[#032282]/90 text-white font-medium py-3 rounded-lg transition-all duration-200 hover:shadow-lg"
                                onClick={() => window.open("/", "_self")}
                            >
                                Got it, thanks!
                            </Button>
                            <Button
                                className="px-6 py-3 border-[#032282] text-[#ffffff] hover:bg-[#032282] hover:text-white transition-all duration-200"

                                onClick={() => {
                                    // Handle contact action
                                    window.open("tel:+2349024675224", "_self")
                                }}
                            >
                                <Phone className="size-4 mr-2" />
                                Call Now
                            </Button>
                        </div>

                        {/* Security notice */}
                        <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-green-500">
                            <div className="flex items-start gap-3">
                                <Shield className="size-5 text-green-600 mt-0.5 shrink-0" />
                                <div>
                                    <p className="text-sm font-medium text-gray-800 mb-1">Your data is secure</p>
                                    <p className="text-sm text-gray-600 leading-relaxed">
                                        We use bank-level encryption and only use your information to assess eligibility and process your
                                        application.
                                    </p>
                                </div>
                            </div>
                        </div>


                        {/* Next steps preview */}
                        <div className="text-center pt-2">
                            <p className="text-xs text-gray-500 mb-2">What happens next?</p>
                            <div className="flex justify-center items-center gap-2 text-xs text-gray-400">
                                <span className="size-2 bg-green-500 rounded-full"></span>
                                <span>Application received</span>
                                <span className="w-8 h-px bg-gray-300"></span>
                                <span className="size-2 bg-gray-300 rounded-full"></span>
                                <span>Team contact</span>
                                <span className="w-8 h-px bg-gray-300"></span>
                                <span className="size-2 bg-gray-300 rounded-full"></span>
                                <span>Full application</span>
                            </div>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </OnboardingPageWrapper>
    );
}
