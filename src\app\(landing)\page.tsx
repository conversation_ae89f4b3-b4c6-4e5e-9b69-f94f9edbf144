"use client"

import Link from "next/link";
import { motion } from 'motion/react'

import { cn } from "@/utils/classNames";
import { LinkButton } from "@/components/core";
import Arrow from "@/icons/Arrow";
import SeedsPennies from "@/icons/SeedsPenniesLogo";

import { CustomersSaySection, FAQSection, FeauturesSlider, FooterSection, HeroBNPLCard, JourneySection, PreFooterSection, StackingCardsSection, TransitioningText } from "./misc/components";
import LogosSlider from "./misc/components/LogosSlider";



export default function LandingPage() {

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };
  const headlineTexts = [
    "Where Financial Freedom Meets Business Growth",
    "Empowering Your Business, One Loan at a Time",
    "Providing swift & ease credit for Unbanked business",
  ];



  return (
    <main className="  wax-w-screen max-md:pb-12">
      <div className="bg-[#000619] md:m-1.5 !mb-0  pb-8 rounded-t-3xl max-md:rounded-none md:rounded-xl md:rounded-bl-none text-white shadow-sm">
        <header className=" flex items-center gap-4 justify-between px-8 p-6 xl:px-16">
          <Link className={cn(" font-semibold text-xl lg:text-2xl")} href="/">
            <SeedsPennies />
          </Link>


          <LinkButton className={cn("hidden md:flex items-center justify-between text-[0.865rem] text-left gap-[7px] px-5 max-w-max", "font-display")} href="/get-started" size="lg" >
            Get Started
            <Arrow className="size-[20px] ml-5 rotate-[-135deg]" />
          </LinkButton>
        </header>


        <motion.section
          className="max-md:flex flex-col py-12 xl:py-16 bg-[url('/images/landing/BG_HORIZONTAL_PATTERN.png')] bg-no-repeat bg-cover"
          initial="hidden"
          variants={containerVariants}
          whileInView="visible"
        >
          <div className="relative flex flex-col md:items-center gap-2 max-md:pl-8 text-center">
            <motion.span className="px-6 py-3 rounded-full text-[#4C88EA] bg-[#081934] max-md:max-w-max" variants={itemVariants}>
              All savings & loans
            </motion.span>
            <TransitioningText
              texts={headlineTexts}
            />
            {/* <motion.h1 className="text-balance text-[clamp(2.4rem,3.5vw,300px)] font-medium text-center max-w-[20ch]" variants={itemVariants}> */}
            {/* Where Financial Freedom Meets Business Growth */}
            {/* </motion.h1> */}
            <p className="md:text-lg text-[#cdd5dde4] max-w-[50ch] text-balance text-left md:text-center">
              Empowering small and medium businesses with fast, flexible financing solutions to turn ambitions into achievements
            </p>
            <motion.div className="flex items-center md:justify-center gap-4 my-8 z-10">
              <LinkButton className={cn("hidden md:flex items-center justify-between text-[0.865rem] text-left gap-[7px] px-5 max-w-max", "font-display")} href="/get-started" size="lg" >
                Get loan
                <Arrow className="size-[20px] ml-5 rotate-[-135deg]" />
              </LinkButton>
              <LinkButton className={cn("flex items-center justify-between text-[0.865rem] text-left gap-[7px] px-5 max-w-max font-display")} href="https://play.google.com/store/apps/details?id=com.libertytech.seedslite" size="lg" target="_blank" variant="secondary" >
                Download App
                <Arrow className="size-[20px] ml-5" />
              </LinkButton>
            </motion.div>
          </div>

          <motion.div
            className="flex max-sm:flex-col max-md:gap-y-7 justify-between md:translate-y-[-40%] px-[5vw] max-w-[1600px] mx-auto"
            initial="hidden"
            variants={containerVariants}
            whileInView="visible"

          >
            <HeroBNPLCard
              description="Own your favorite home appliances, gadgets, and work tools today, and enjoy the convenience of paying in flexible, easy installments."
              images={[
                {
                  alt: "BNPL1",
                  src: "/images/landing/BNPL1.jpg",
                },
                {
                  alt: "BNPL2",
                  src: "/images/landing/BNPL2.jpg",
                },
              ]}
              title={
                <>
                  Flex buy
                  <span className="text-xs text-[#CDD5DD] ml-1.5">
                    (Buy now, pay later)
                  </span>
                </>
              }
            />
            <HeroBNPLCard
              description="Save ‘small small’ and enjoy the convenience of having your favorite items delivered to you effortlessly once your savings goal is met."
              images={[
                {
                  alt: "SEEDS1",
                  src: "/images/landing/SEEDS1.jpg",
                },
                {
                  alt: "SEEDS2",
                  src: "/images/landing/SEEDS2.png",
                },
              ]}
              reverseStepping={true}
              title={
                <>
                  70 seeds
                  <span className="text-xs text-[#CDD5DD] ml-1.5">
                    (Save now, pay later)
                  </span>
                </>
              }
            />

          </motion.div>
        </motion.section>
        <StackingCardsSection />
        <LogosSlider />
      </div>


      <FeauturesSlider />
      <JourneySection />
      <FAQSection />
      <CustomersSaySection />


      <PreFooterSection />
      <FooterSection />

      <LinkButton className={cn("fixed md:hidden bottom-0 w-screen z-20 bg-[#0854C2] !text-white hover:!opacity-100 text-main py-4 !rounded-none text-lg", "font-display")} href="/get-started" variant="default">
        Get started
      </LinkButton>
    </main>
  );
}
