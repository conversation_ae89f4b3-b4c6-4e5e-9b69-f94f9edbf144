'use client'

import { LoanOptionsPageWrapper } from '../../misc/components/LoanOptionsPageWrapper';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { NextOfKinFormDetails } from '../../misc/components/forms/NextOfKinFormDetails';



export default function Transactions() {

    const { emailAddress, phone_number } = useOnboardingPlan();

    return (
        <>

            <LoanOptionsPageWrapper heading={"Next of kin"}>
                <div className='p-4 overflow-auto'>
                    <NextOfKinFormDetails
                        email={String(emailAddress)}
                        phone={String(phone_number)}
                    />
                </div>
            </LoanOptionsPageWrapper>
        </>
    );
}
