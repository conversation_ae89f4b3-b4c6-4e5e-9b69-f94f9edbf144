@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --light-bg: rgb(246, 248, 253);
    --dash-dark-bg: #07112d;
    --dash-light-bg: #f8f9fb;
    --success: #18a201;
    --divider-line: #d6d6d6;

    --light-accent-bg: #eaf2ff;

    --dark-text: #242424;
    --light-text: #37474f;
    --light-dark-text: #e8e8e8;

    --label-text: #4e4e4e;

    --solid-underline: #5879fd;

    --main-solid: #032282;
    --main-solid-light: #073d9f;
    --main-bg: #f2f5ff;

    --input-bg: #f5f7f9;
    --input-placeholder: #818181;

    --card-border: #ececec;

    --sidebar-link-active: #192749;

    --sb-track-color: #d1d5db;
    --sb-thumb-color: #6b7280;
    --sb-size: 10px;

    --marketing-dark: #002756;
    --marketing-light: #eaf2ff;
    --marketing-light-2: #eaf2ff;
    --marketing-light-text: #768eaa;

    --test-gradient: #9a580d;

    /* Shadcn Colors */

    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    overflow: auto;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .text-balance {
    text-wrap: balance;
  }

  .px-marketing-default {
    @apply mx-auto w-full max-w-[100rem] px-4 md:px-10 lg:px-16 xl:px-[6.25rem];
  }

  .items-safe-center {
    align-items: safe center;
  }
}

/* Hide textual date representation in React Day Picker. Selects are used instead. */
/* https://github.com/shadcn-ui/ui/issues/546#issuecomment-1633100711 */
.rdp-vhidden {
  @apply hidden;
}

* {
  scroll-behavior: smooth;
}

.blue__white__gradient {
  border-radius: 10px;
  background: linear-gradient(27deg, #ebeffb 0%, #fff 100%);
}

.light__blue__gradient {
  border-radius: 12px !important;
  background: linear-gradient(
    45deg,
    #5879fd -16.26%,
    #5879fd 51.75%
  ) !important;
}

/* ZIG-ZAG FROM MODAL */

.zig-zag-top:before {
  background: linear-gradient(
      -45deg,
      #fff 16px,
      red 16px,
      blue 16px,
      transparent 0
    ),
    linear-gradient(45deg, #fff 16px, transparent 0);
  background-position: left top;
  background-repeat: repeat-x;
  background-size: 20px;
  content: ' ';
  display: block;
  height: 25px;
  width: 100%;
  position: relative;
  top: -1.3rem;
  border: none;
}

.zig-zag-bottom:after {
  background: linear-gradient(-45deg, transparent 16px, #fff 0),
    linear-gradient(45deg, transparent 16px, #fff 0);
  background-repeat: repeat-x;
  background-position: left bottom;
  background-size: 20px;
  content: '';
  display: block;
  width: 100%;
  height: 30px;
  position: relative;
  bottom: -1.3rem;
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

input[type='radio'] {
  appearance: none;
  background-color: #fff;
  margin: 0;
  width: 1rem;
  height: 1rem;
  border: 0.095rem solid var(--main-solid);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

input[type='radio']::before {
  content: '';
  display: block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  background-color: var(--main-solid);
}

input[type='radio']:checked::before {
  transform: scale(1);
}

ol.disc > li {
  list-style-type: disc;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: -0.25rem;

  /* list-style-image: ; */
  &::marker {
    font-size: 1.35rem;
    color: #032282;
  }
}

/* For features spinner area on landing page */
.features-roller-container {
  --show-ball-scroll-level: 0.1;
}

@media (min-width: 1280px) {
  .features-roller-container {
    --show-ball-scroll-level: 0.3;
  }
}

.login-gradient {
  background: linear-gradient(224deg, #000 50.08%, #032282 104.44%);
}

input.login-no-chrome-autofill-bg:-webkit-autofill,
input.login-no-chrome-autofill-bg:-webkit-autofill:hover,
input.login-no-chrome-autofill-bg:-webkit-autofill:focus,
input.login-no-chrome-autofill-bg:-webkit-autofill:active {
  -webkit-background-clip: text;
  -webkit-text-fill-color: #ffffff;
  transition: background-color 5000s ease-in-out 0s;
  box-shadow: inset 0 0 0px 30px rgba(255, 255, 255, 0.3);
}

input.login-autofill-text:-webkit-autofill::first-line {
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system,
    BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: 1rem;
}

/* Styles for responsive datatable */
@media only screen and (max-width: 64rem) {
  .responsive-table-container table,
  .responsive-table-container thead,
  .responsive-table-container tbody,
  .responsive-table-container th,
  .responsive-table-container td,
  .responsive-table-container tr {
    display: block;
  }

  /* Hide actual headings from sighted users */
  .responsive-table-container thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .responsive-table-container tr {
    display: grid;
    grid-template-columns: calc(50% - 0.5rem) calc(50% - 0.5rem);
    padding: 1rem 0;
    gap: 0.5rem;
    overflow-x: auto;
  }

  .responsive-table-container td::before {
    /* aria-label has no advantage, it won't be read inside a table */
    content: attr(data-header);
    font-size: 0.75rem;
    display: block;
    opacity: 0.5;
    text-transform: uppercase;
  }

  .responsive-table-container tr.responsive-table-loader-row {
    display: unset !important;
  }

  .responsive-table-container tr.responsive-table-loader-cell {
    display: unset !important;
  }
}

::-webkit-scrollbar {
  width: 0px !important;
  height: 10px !important;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px white !important;
  border-radius: 10px !important;
}

::-webkit-scrollbar-thumb {
  background: radial-gradient(
    59.67% 59.67% at 53.97% 40.33%,
    #4c1961 0%,
    #270336 100%
  ) !important;
  border-radius: 10px !important;
}

@layer utilities {
  .animate-slide {
    animation: slide 10s linear infinite;
  }

  .animate-reverseSlide {
    animation: reverseSlide 10s linear infinite;
  }

  .animate-itemSlide {
    animation: itemSlide 10s linear infinite;
    left: 100%;
  }

  .animate-reverseItemSlide {
    animation: reverseItemSlide 10s linear infinite;
    left: calc(var(--width) * -1);
  }
}

@keyframes slide {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(var(--width) * var(--quantity) * -1));
  }
}

@keyframes reverseSlide {
  from {
    transform: translateX(calc(var(--width) * var(--quantity) * -1));
  }
  to {
    transform: translateX(0);
  }
}

@keyframes itemSlide {
  from {
    left: 100%;
  }
  to {
    left: calc(var(--width) * -1);
  }
}

@keyframes reverseItemSlide {
  from {
    left: calc(var(--width) * -1);
  }
  to {
    left: 100%;
  }
}

