{"name": "liberty-seeds-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@ianvs/prettier-plugin-sort-imports": "^4.1.1", "@number-flow/react": "^0.4.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@tanstack/react-query": "^5.24.1", "@tanstack/react-table": "^8.13.0", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.0", "date-fns": "^3.3.1", "embla-carousel-auto-scroll": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.5.1", "eslint-config-prettier": "^9.1.0", "framer-motion": "^11.0.6", "lucide-react": "^0.468.0", "motion": "^11.14.0", "next": "14.2.5", "nextjs-toploader": "^1.6.6", "prettier": "^3.2.5", "react": "^18", "react-day-picker": "^8.10.0", "react-dom": "^18", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.4.1", "react-pin-input": "^1.3.1", "react-query": "^3.39.3", "react-use-measure": "^2.1.1", "react-wrap-balancer": "^1.1.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.1.0", "autoprefixer": "^10.0.1", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "^14.1.0", "eslint-plugin-check-file": "^2.7.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-tailwindcss": "^3.14.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}