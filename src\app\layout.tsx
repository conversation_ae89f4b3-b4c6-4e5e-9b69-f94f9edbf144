import type { <PERSON>ada<PERSON> } from 'next';
import { DM_Sans, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google';
import './globals.css';
import ReactQueryProvider from '@/lib/reactQuery';
import { cn } from '@/utils/classNames';
import { AuthProvider } from '@/contexts/authentication';
import { Toaster } from 'react-hot-toast';
import Script from 'next/script';

// const dm_sans = DM_Sans({ subsets: ['latin'] });
const fontSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'DM_Sans',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontNunito = Nunito({
  subsets: ['latin'],
  variable: '--font-nunito',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Nunito',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontManrope = Manrope({
  subsets: ['latin'],
  variable: '--font-manrope',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Manrope',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});


export const metadata: Metadata = {
  title: 'Seeds Onlending',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <head>
        {/* NEW FACEBOOK PIXWL */}
        <Script
          dangerouslySetInnerHTML={{
            __html: `!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '1271871761339493');
fbq('track', 'PageView');`,
          }}
          id="show-facebook-pixel-new"
        />

        <link href="/favicon.ico" rel="icon" sizes="any" />
      </head>
      <body
        className={cn(
          'font-sans',
          fontSans.variable,
          fontManrope.variable,
          fontNunito.variable,
        )}>

        <Toaster
          containerStyle={{
            zIndex: 99999,
          }}
          position="top-center"
          toastOptions={{
            style: {
              zIndex: 99999,
            },
          }}
        />

        <ReactQueryProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ReactQueryProvider>

     {/* NEW FACEBOOK PIXEL */}
     <noscript
          dangerouslySetInnerHTML={{
            __html: `<img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1271871761339493&ev=PageView&noscript=1"/>`,
          }}
        />

      </body>
    </html>
  );
}
