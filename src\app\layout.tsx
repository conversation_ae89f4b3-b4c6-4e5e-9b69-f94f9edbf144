import type { <PERSON>ada<PERSON> } from 'next';
import { DM_Sans, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google';
import './globals.css';
import ReactQueryProvider from '@/lib/reactQuery';
import { cn } from '@/utils/classNames';
import { AuthProvider } from '@/contexts/authentication';
import { Toaster } from 'react-hot-toast';

// const dm_sans = DM_Sans({ subsets: ['latin'] });
const fontSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'DM_Sans',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontNunito = Nunito({
  subsets: ['latin'],
  variable: '--font-nunito',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Nunito',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontManrope = Manrope({
  subsets: ['latin'],
  variable: '--font-manrope',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Manrope',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});


export const metadata: Metadata = {
  title: 'Seeds Onlending',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <body
        className={cn(
          'font-sans',
          fontSans.variable,
          fontManrope.variable,
          fontNunito.variable,
        )}>

        <Toaster
          containerStyle={{
            zIndex: 99999,
          }}
          position="top-center"
          toastOptions={{
            style: {
              zIndex: 99999,
            },
          }}
        />

        <ReactQueryProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ReactQueryProvider>

      </body>
    </html>
  );
}
