
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface PlanData {
  status: boolean
  data: PlanEntities[]
}

export interface PlanEntities {
  min_days: number
  max_days: number
  rate: string
}


export const getOnlendingPlanNoToken = async (): Promise<PlanData> => {
  
      
  const { data } = await savingsAxios.get('/onlending/interest-for-ranges/');
  return data;
};

export const useOnlendingPlanNoToken = () =>
  useQuery('on-lending-no-token', () => getOnlendingPlanNoToken(), );
 