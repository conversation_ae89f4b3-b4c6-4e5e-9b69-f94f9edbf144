import * as React from 'react';


import { Button, } from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { LendingOptions } from './modals/LendingOptions';
import clsx from 'clsx';


export function StartLending() {
    const {
        state: isLendingOptionOpen,
        setState: setLendingOptionState,
        setTrue: openLendingOption,
    } = useBooleanStateControl();

    return (
        <main
            className={clsx(
                'fixed bottom-0 z-40 mx-auto max-w-[34.375rem] flex w-full flex-row items-center justify-between bg-white px-4  py-2'
            )}
        >
            <div className='w-full flex items-center justify-center'>
                <Button className='py-4 text-base font-semibold' size="fullWidth" onClick={openLendingOption}>
                    Start lending
                </Button>
            </div>


            <LendingOptions
                heading={'Lending options'}
                isLendingOptionsOpen={isLendingOptionOpen}
                setLendingOptionsState={setLendingOptionState}
            />
        </main>
    );
}
