import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface requestPasswordDTO {
    entry_type: string;
    identifier: string;
}

const requestResetPassword = (requestPasswordDTO: requestPasswordDTO): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/first_reset_login_pin/`, requestPasswordDTO);
};

export const useRequestResetPassword = () => {
  return useMutation('request-reset', requestResetPassword, {});
};