import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';

export interface VerifyBVNDTO {
  bvn_number: string;
  phone_number: string;
  otp_type: string;
}

export interface VerifyBVNResponse {
  status: string;
  message: string;
}


const verifyBVN = (verifyBVNDTO: VerifyBVNDTO): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/kyc/user/verify_bvn/`, verifyBVNDTO);
};

export const useVerifyBVN = () => {
  return useMutation('verifyBvn', verifyBVN, {});
};