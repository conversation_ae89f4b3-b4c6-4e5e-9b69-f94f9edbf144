'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
import type { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import {
  Button,
  ErrorModal,
  FormError,
  Input,
  LinkButton,
  LoaderModal,

} from '@/components/core';
import { cn } from '@/utils/classNames';
import { usePostOtpLogin } from '../../../misc';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils';
import React from 'react';

interface GetStartedProps {
  referral_code: string | null
}


const GetStartedFormSchema = z.object({
  phone_number: z
    .string({ required_error: 'Please enter your phone number.' })
    .trim()
    .min(1, { message: 'Please enter your phone number.' }),

  otp: z
    .string({ required_error: 'Please enter otp' })
    .trim()
    .min(1, { message: 'Please enter otp' }),
});

export type GetStartedFormValues = z.infer<typeof GetStartedFormSchema>;

export function PhoneNumberLoginForm({ referral_code }: GetStartedProps) {
  const router = useRouter();
  const {
    control,
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = useForm<GetStartedFormValues>({
    resolver: zodResolver(GetStartedFormSchema),
  });

  const { state: isLoaderModalOpen, setTrue: _openLoaderModal } =
    useBooleanStateControl();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const user_phone_number = useWatch({
    control,
    name: 'phone_number',
  });


  // // Custom validation and normalization function for phone number
  // const validatePhoneNumber = (value: string) => {
  //   // Normalize the phone number to remove leading zeros and non-numeric characters
  //   const normalizedPhoneNumber = value.replace(/[^\d]/g, '');

  //   // Check if the phone number starts with "234" or "+234" and normalize it to start with "0"
  //   if (normalizedPhoneNumber.startsWith('234')) {
  //     return '0' + normalizedPhoneNumber.slice(3);
  //   } else if (normalizedPhoneNumber.startsWith('+234')) {
  //     return '0' + normalizedPhoneNumber.slice(4);
  //   }

  //   return normalizedPhoneNumber; // Return the original value if it doesn't match the pattern
  // };


  // Function to format the phone number
  const formatPhoneNumber = (value: string) => {
    // Check if the value starts with "0"
    if (value?.startsWith('234') || value?.startsWith('2') || value?.startsWith('+')) {
      // Remove the first "0" and add "234" in front of it
      const formattedNumber = `0${value.slice(1)}`;
      return formattedNumber;
    }
    return value;
  };

  const enteredPhoneNumberValue = useWatch({
    control,
    name: 'phone_number',
  });

  // Watch for changes in the phone number and format it
  React.useEffect(() => {
    const formattedNumber = formatPhoneNumber(enteredPhoneNumberValue);
    // Update the form's value with the formatted number
    setValue('phone_number', formattedNumber);
  }, [enteredPhoneNumberValue, setValue]);


  const { mutate: postSignUp, isLoading: isSignUpLoading } = usePostOtpLogin();



  const onBVNFormSubmit = (submittedData: GetStartedFormValues) => {


    postSignUp(
      {
        otp: submittedData.otp,
        phone_number: submittedData.phone_number,
        referal_code: referral_code
      },
      {
        onSuccess: () => {
          router.push('/dashboard');

        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );
  };

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen} />

      <form
        className="relative z-10"
        onSubmit={handleSubmit(onBVNFormSubmit)}
      >
        <p className='text-white text-xs mb-1'>Enter phone number you want to login with</p>
        <div>
          <Label className="sr-only" htmlFor="phone">
            Phone Number
          </Label>
          <Input
            className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
            id="phone"
            placeholder="Enter phone number"
            type="text"
            {...register('phone_number')}
          />
          {errors?.phone_number && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.phone_number.message}
            />
          )}
        </div>

        <div className={cn(
          user_phone_number?.length >= 11 ? 'block' : 'hidden'
        )}>
          <div className='w-full rounded-lg p-4 bg-[#ffffff46] mt-2 flex items-center font-medium text-white justify-center flex-col'>
            <p className='text-center text-xs max-w-[272px]'>Dial this USSD code with the phone number you entered above and enter the OTP in the  field below.</p>
            <p className='mt-[9px] font-extrabold text-xl'>*347*180*24#</p>
          </div>

          <div className='mt-4'>
            <Label className="sr-only" htmlFor="otp">
              Enter OTP
            </Label>
            <Input
              className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
              id="otp"
              placeholder="Enter OTP"
              type="text"
              {...register('otp')}
            />
            {errors?.otp && (
              <FormError
                className="bg-red-900/40 text-white"
                errorMessage={errors.otp.message}
              />
            )}
          </div>
        </div>

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isSignUpLoading}
          type="submit"
          variant="white"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            {isSignUpLoading ? 'Loading' : 'Login'}

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
        <LinkButton
          className="mt-6 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
          href="/get-started"
          type="button"
          variant="outlined"
        >
          <span className="text-xxs font-normal md:text-xs">
            Don&apos;t have an account?
          </span>
          <span className="text-sm md:text-base">Sign up</span>
        </LinkButton>
      </form>


      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}