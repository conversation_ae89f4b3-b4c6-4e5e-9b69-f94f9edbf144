
import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';

import { useQuery } from 'react-query';

export interface PlanData {
  status: boolean
  data: PlanEntities[]
}

export interface PlanEntities {
  min_days: number
  max_days: number
  rate: string
}


export const getOnlendingPlan = async (email: string): Promise<PlanData> => {
  
  const headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  
      
  const { data } = await savingsAxios.get('/onlending/interest-for-ranges/', {headers});
  return data;
};

export const useOnlendingPlan = (email: string) =>
  useQuery('on-lending', () => getOnlendingPlan(email), );
 