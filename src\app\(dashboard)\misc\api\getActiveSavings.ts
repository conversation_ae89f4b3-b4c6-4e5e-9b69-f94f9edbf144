import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface ActiveSavingsData {
  status: boolean
  data: ActiveSavingsEntity
}

export interface ActiveSavingsEntity {
  total_balance: number
  total_savings: number
}


export const getActiveSavings = async (email: string): Promise<ActiveSavingsData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get('/onlending/bank-account/', {headers});
  return data;
};

export const useActiveSavings = (email: string) =>
  useQuery('on-lending-ActiveSavings', () => getActiveSavings(email), );
 