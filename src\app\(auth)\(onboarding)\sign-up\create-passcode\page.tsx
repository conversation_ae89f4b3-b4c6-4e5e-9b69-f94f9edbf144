import * as React from 'react';

import { OnboardingPageWrapper } from '../../misc/components';
import { CreatePasscodeForm } from './misc/components';

export default function CreatePasscode({
  searchParams,
}: {
  searchParams: { email: string; phone: string };
}) {
  const { email, phone } = searchParams;

  return (
    <>
      <div className="mx-auto max-w-[32.375rem] px-2 md:hidden">
        <p className="relative mb-2 ml-auto w-max text-white md:hidden">3/7</p>
      </div>

      <OnboardingPageWrapper
        heading="Create your passcode"
        subHeading="This will help you log in faster."
      >
        <CreatePasscodeForm email={email} phone={phone} />
      </OnboardingPageWrapper>
    </>
  );
}
