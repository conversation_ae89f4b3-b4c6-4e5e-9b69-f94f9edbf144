import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';



export interface AllPlansData {
  status: boolean
  count: number
  next: string
  previous: string
  results: AllPlansResults
}

export interface AllPlansResults {
  id: number
  name: string
  onlending_type: string
  duration: number
  target: number
  interest_rate: number
  periodic_amount: number
  frequency: string
  hour: string
  maturity_date: string
  estimated_amount: number
  total_interest_earned: number
  quotation_id: string
  amount_saved: number
  interest_type: string
  is_activated: boolean
  completed: boolean
  is_active: boolean
  interest_paid: boolean
  user: number
  ajo_user: string
  transactions: Transaction[]
}

export interface Transaction {
  id: number
  status: string
  amount: number
  date_created: string
  transaction_id: string
  description: string
  quotation_id: string
  transaction_form_type: string
  transaction_date_completed: string
  transaction_type: string
  wallet_type: string
  wallet_balance_before: number
  wallet_balance_after: number

}


export interface User {
  email: string
}

export const getAllPlanId = async (email: string, id: number): Promise<AllPlansData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get(`/onlending/plan-details?id=${id}`, {headers});
  return data;
};

export const useAllPlansId = (email: string, id: number) =>
  useQuery('all-plans-id', () => getAllPlanId(email, id), );
 