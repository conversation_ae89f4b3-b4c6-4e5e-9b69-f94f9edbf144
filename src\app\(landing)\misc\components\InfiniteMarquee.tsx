'use client'

import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'

interface Logo {
    src: string
    alt: string
}

interface LogoMarqueeProps {
    logos?: Logo[]
    speed?: number
    direction?: 'left' | 'right'
}
// const DEFUALT_BRANDS = ["/images/landing/brands/liberty-assured.png", "/images/landing/brands/liberty-pay.png", "/images/landing/brands/liberty-tech.png", "/images/landing/brands/nem-health.png", "/images/landing/brands/vfd-bank.png",]

const BRANDS = [
    { src: "/images/landing/brands/liberty-assured.png", alt: "Liberty Assured" },
    { src: "/images/landing/brands/liberty-pay.png", alt: "Liberty Pay" },
    { src: "/images/landing/brands/liberty-tech.png", alt: "Liberty Tech" },
    { src: "/images/landing/brands/nem-health.png", alt: "Nem Health" },
    { src: "/images/landing/brands/vfd-bank.png", alt: "VFD Bank" },
]
const LogoMarquee: React.FC<LogoMarqueeProps> = ({
    logos = BRANDS,
    speed = 1,
    direction = 'left',
}) => {
    return (
        <div>
            <p className="text-center md:text-lg text-[#ffffff71]">
                Seeds is support and powered by
            </p>
            <div className="w-full lg:w-[90%] max-w-[1500px] mx-auto overflow-hidden py-4">
                <motion.div
                    animate={{
                        x: direction === 'left' ? ['0%', '-100%'] : ['-100%', '0%'],
                    }}
                    className="flex whitespace-nowrap gap-x-12"
                    transition={{
                        x: {
                            repeat: Infinity,
                            repeatType: 'loop',
                            duration: logos.length * speed ,
                            ease: 'linear',
                        },
                    }}
                >
                    {[...logos, ...logos, ...logos, ...logos].map((logo, index) => (
                        <div className="relative flex items-center justify-center h-[50px] aspect-[initial] w-auto min-w-[130px]" key={index} >
                            <Image
                                alt={logo.alt}
                                className="max-w-none w-auto h-full"
                                objectFit='contain'
                                src={logo.src}
                                fill
                            />


                        </div>
                    ))}
                </motion.div>
            </div>
        </div>
    )
}

export default LogoMarquee;