import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';

import { cn } from '@/utils/classNames';
import { ButtonNavigation } from './ButtomNavigation';

export function ProfilePageWrapper({
  children,
  heading,
  isCentered = false,
}: {
  children: React.ReactNode;
  heading: React.ReactNode;
  isCentered?: boolean;
}) {
  return (
    <>
      <main className="relative mx-auto max-w-[34.375rem] bg-white  md:h-auto md:min-h-0 md:w-full md:grow-0 md:overflow-y-auto ">
        <div className="relative md:h-[812px]  pb-5 ">
          <div
            className={cn(
              'absolute inset-0 rounded-[5px] bg-[#F8F9FB]'
            )}
          />
          <h1
            className={cn(
              'relative flex gap-2 items-center mb-1 font-clash text-xl font-semibold leading-[normal] px-4 py-7 text-white bg-[#032282]',
              isCentered && 'text-center'
            )}
          >
            <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
              <circle cx="16" cy="16" fill="#DFEEFF" r="16" />
              <path d="M20.8251 24.9586H11.1751C8.89175 24.9586 7.04175 23.1003 7.04175 20.817V14.642C7.04175 13.5086 7.74175 12.0836 8.64175 11.3836L13.1334 7.88364C14.4834 6.83364 16.6417 6.78364 18.0417 7.76697L23.1917 11.3753C24.1834 12.067 24.9584 13.5503 24.9584 14.7586V20.8253C24.9584 23.1003 23.1084 24.9586 20.8251 24.9586ZM13.9001 8.86697L9.40841 12.367C8.81675 12.8336 8.29175 13.892 8.29175 14.642V20.817C8.29175 22.4086 9.58341 23.7086 11.1751 23.7086H20.8251C22.4167 23.7086 23.7084 22.417 23.7084 20.8253V14.7586C23.7084 13.9586 23.1334 12.8503 22.4751 12.4003L17.3251 8.79197C16.3751 8.1253 14.8084 8.15864 13.9001 8.86697Z" fill="#032282" />
              <path d="M16 21.625C15.6583 21.625 15.375 21.3417 15.375 21V18.5C15.375 18.1583 15.6583 17.875 16 17.875C16.3417 17.875 16.625 18.1583 16.625 18.5V21C16.625 21.3417 16.3417 21.625 16 21.625Z" fill="#032282" />
            </svg>



            <Balancer className={cn(isCentered && 'text-center')}>
              {heading}
            </Balancer>
          </h1>

          <div className='py-3 px-4  relative'>
            {children}
          </div>



          <ButtonNavigation />

        </div>


      </main >
    </>
  );
}
