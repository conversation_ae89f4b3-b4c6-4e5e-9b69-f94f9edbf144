'use client';

import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/core';

interface SuccessMainModalProps {
  isSuccessMainModalOpen: boolean;
  setSuccessMainModalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  subheading: string;
  closeButtonReplacement?: React.ReactNode;
  children?: React.ReactNode;
}

export function SuccessMainModal({
  isSuccessMainModalOpen,
  setSuccessMainModalState,
  heading,
  subheading,
  children,
}: SuccessMainModalProps) {
  return (
    <Dialog open={isSuccessMainModalOpen} onOpenChange={setSuccessMainModalState}>
      <DialogContent>

        <DialogBody className="p-0 text-center">
          <div className="px-8 pb-6 pt-10">
            {/* <Image
              alt={heading}
              className="mx-auto mb-4"
              height={132}
              src="/images/success-illustrations/create-company-success.png"
              width={175}
            /> */}
            <div className='w-full justify-center items-center flex'>
              <svg className='size-20' fill="none" height="51" viewBox="0 0 51 51" width="51" xmlns="http://www.w3.org/2000/svg">
                <circle cx="25.5" cy="25.5" r="22.5" stroke="#D8FFEC" strokeWidth="6" />
                <mask fill="white" id="path-2-inside-1_160_304">
                  <path d="M51 25.5C51 39.5833 39.5833 51 25.5 51C11.4167 51 0 39.5833 0 25.5C0 11.4167 11.4167 0 25.5 0C39.5833 0 51 11.4167 51 25.5ZM5.20855 25.5C5.20855 36.7067 14.2933 45.7915 25.5 45.7915C36.7067 45.7915 45.7915 36.7067 45.7915 25.5C45.7915 14.2933 36.7067 5.20855 25.5 5.20855C14.2933 5.20855 5.20855 14.2933 5.20855 25.5Z" />
                </mask>
                <path d="M51 25.5C51 39.5833 39.5833 51 25.5 51C11.4167 51 0 39.5833 0 25.5C0 11.4167 11.4167 0 25.5 0C39.5833 0 51 11.4167 51 25.5ZM5.20855 25.5C5.20855 36.7067 14.2933 45.7915 25.5 45.7915C36.7067 45.7915 45.7915 36.7067 45.7915 25.5C45.7915 14.2933 36.7067 5.20855 25.5 5.20855C14.2933 5.20855 5.20855 14.2933 5.20855 25.5Z" mask="url(#path-2-inside-1_160_304)" stroke="#12B669" strokeWidth="22" />
                <path d="M25.5003 45.0827C36.3159 45.0827 45.0837 36.3149 45.0837 25.4993C45.0837 14.6838 36.3159 5.91602 25.5003 5.91602C14.6847 5.91602 5.91699 14.6838 5.91699 25.4993C5.91699 36.3149 14.6847 45.0827 25.5003 45.0827Z" fill="#12B669" />
                <path d="M22.7188 32.511C22.3272 32.511 21.9551 32.3543 21.6809 32.0801L16.1388 26.538C15.5709 25.9701 15.5709 25.0301 16.1388 24.4622C16.7067 23.8943 17.6467 23.8943 18.2147 24.4622L22.7188 28.9664L32.7847 18.9005C33.3526 18.3326 34.2926 18.3326 34.8605 18.9005C35.4284 19.4685 35.4284 20.4085 34.8605 20.9764L23.7567 32.0801C23.4826 32.3543 23.1105 32.511 22.7188 32.511Z" fill="white" />
              </svg>
            </div>


            <DialogTitle className="font-heading text-xl mt-3">
              {heading}
            </DialogTitle>
            <DialogDescription className="">{subheading}</DialogDescription>
          </div>

          {children}
        </DialogBody>
      </DialogContent>
    </Dialog>
  );
}
