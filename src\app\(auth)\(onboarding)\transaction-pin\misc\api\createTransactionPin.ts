// import { adminLoanAxios } from '@/lib/axios';

// import { useMutation, useQueryClient } from '@tanstack/react-query';

// export interface CreateTransactionPinDTO {
//   transaction_pin: string;
//   transaction_pin_retry: string;
// }

// export interface CreateTransactionPinResponse {
//   status: string;
//   message: string;
// }

// const createTransactionPin = async (
//   createTransactionPinDTO: CreateTransactionPinDTO
// ) => {
//   const response = await adminLoanAxios.post(
//     '/agency/user/create_transaction_pin/',
//     createTransactionPinDTO
//   );
//   return response.data as CreateTransactionPinResponse;
// };

// export const useCreateTransactionPin = () => {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: createTransactionPin,
//     onSuccess: () => {
//       queryClient.invalidateQueries({
//         queryKey: ['user-details'],
//       });
//     },
//   });
// };

import { adminLoanAxios } from '@/lib/axios';

import { useMutation, useQueryClient } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface CreateTransactionPinResponse {
  transaction_pin: string;
  transaction_pin_retry: string;
}

const createTransactionPin = (createTransactionPinResponse: CreateTransactionPinResponse): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/create_transaction_pin/`, createTransactionPinResponse);
};

export const useCreateTransactionPin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createTransactionPin,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['createTransactionPin'],
      });
    },
  });
  
  // return useMutation('createTransactionPin', createTransactionPin, {});
};
