import { OnboardingPageWrapper } from '../../misc/components';
import { NextOfKinForm } from './misc/components';

export default function GetStarted({
  searchParams,
}: {
  searchParams: { email: string, phone: string };
}) {
  const { email, phone } = searchParams;

  return (
    <>
      <div className='bg-[#000D36] relative p-6 block md:hidden'>
        <h1 className='font-heading font-semibold text-xl tracking-[2%] text-white'>Onlending enrollment </h1>
        <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-2'>
          In order to know you more and determine your
          lending capabilities please complete the details
          below to start lending
        </p>
      </div>

      <OnboardingPageWrapper
        heading="Next of kin"
        subHeading="Kindly provide details of your next of kin and 
        mother’s maiden name below"
      >
        <NextOfKinForm
          email={email}
          phone={phone}
        />
      </OnboardingPageWrapper>
    </>
  );
}
