import { cn } from '@/utils/classNames';
import { Button } from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { convertNumberToNaira } from '@/utils/currency';
import { useOnboardingPlan } from '@/store/onBoardingPlan';

import * as React from 'react';

import { Balancer } from 'react-wrap-balancer';

import { useRouter } from 'next/navigation';

import { useAllPlansId } from '../api/getAllPlanId';

import { ButtonNavigation } from './ButtomNavigation';
import { formatDateString } from '@/utils/formatShortDate';

export function LendDetailPageWrapper({
  children,
  heading,
  isCentered = false,
  id,
}: {
  children: React.ReactNode;
  heading: React.ReactNode;
  isCentered?: boolean;
  id: number;
}) {
  const router = useRouter();


  const { state: isShown, toggle: toggleShow } = useBooleanStateControl(true);

  const { emailAddress } = useOnboardingPlan();

  const { data: specificPlan } = useAllPlansId(emailAddress as string, Number(id))


  return (
    <>
      <main className="relative mx-auto max-w-[34.375rem]  md:h-auto md:min-h-0 md:w-full md:grow-0 md:overflow-y-auto ">
        <div className="relative md:h-[812px]  py-5 ">
          <div
            className={cn(
              'absolute inset-0 rounded-[5px] bg-[#F1F8FF]'
            )}
          />
          <h1
            className={cn(
              'relative flex gap-2 px-4 items-center mb-1 font-clash text-xl font-semibold leading-[normal] text-[#032282]',
              isCentered && 'text-center'
            )}
          >
            <Button className='p-0 cursor-pointer' variant="unstyled" onClick={() => {
              router.back()
            }}>
              <svg fill="none" height="34" viewBox="0 0 34 34" width="34" xmlns="http://www.w3.org/2000/svg">
                <rect height="32" rx="16" stroke="#032282" width="32" x="1" y="1" />
                <mask height="32" id="mask0_17281_15170" maskUnits="userSpaceOnUse" width="32" x="1" y="1">
                  <rect fill="white" height="32" rx="16" width="32" x="1" y="1" />
                </mask>
                <g mask="url(#mask0_17281_15170)">
                  <path clipRule="evenodd" d="M14.1716 19.8284C12.6615 18.3184 12.6112 15.9014 14.0206 14.3309L14.1716 14.1716L18.291 10.2929C18.6815 9.90237 19.3147 9.90237 19.7052 10.2929C20.0657 10.6534 20.0934 11.2206 19.7884 11.6129L19.7052 11.7071L15.5858 15.5858C14.8458 16.3257 14.8069 17.5012 15.469 18.287L15.5858 18.4142L19.7052 22.2929C20.0958 22.6834 20.0958 23.3166 19.7052 23.7071C19.3447 24.0676 18.7775 24.0953 18.3852 23.7903L18.291 23.7071L14.1716 19.8284Z" fill="#032282" fillRule="evenodd" />
                </g>
              </svg>
            </Button>

            <Balancer className={cn(isCentered && 'text-center')}>
              {heading}
            </Balancer>
          </h1>
          <div className='px-4'>

            <div className='relative rounded-10 bg-white p-4 mt-5'>
              <div
                className={cn(
                  "flex min-w-0 grow flex-col justify-between rounded-xl bg-[#032282] bg-[url('/images/diagonal-triangles.svg')] bg-contain bg-right bg-no-repeat p-6 text-white sm:p-3 md:p-6"
                )}
              >
                <div className="mb-3 flex items-end justify-center gap-2 md:gap-6">
                  <div>
                    <div className='flex items-end gap-[9px]'>
                      <p className="overflow-x-auto">
                        <span className="mb-0.5 block text-xs text-[#cdcdcd]">
                          Lending balance
                        </span>

                        <span className="block overflow-x-auto">
                          <span className="font-heading text-xl font-semibold">
                            {isShown
                              ? `₦${convertNumberToNaira(Number(specificPlan?.results.target), false) || 0}`
                              : '****'}
                          </span>
                        </span>
                      </p>

                      <Button
                        className="shrink-0 pb-0.5"
                        size="unstyled"
                        variant="unstyled"
                        onClick={toggleShow}
                      >
                        <span className="sr-only">
                          {isShown ? 'Hide balance' : 'Show balance'}
                        </span>
                        <svg
                          fill="none"
                          height={24}
                          viewBox="0 0 24 24"
                          width={24}
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 16.33c-2.39 0-4.33-1.94-4.33-4.33S9.61 7.67 12 7.67s4.33 1.94 4.33 4.33-1.94 4.33-4.33 4.33Zm0-7.16c-1.56 0-2.83 1.27-2.83 2.83s1.27 2.83 2.83 2.83 2.83-1.27 2.83-2.83S13.56 9.17 12 9.17Z"
                            fill="#fff"
                          />
                          <path
                            d="M12 21.02c-3.76 0-7.31-2.2-9.75-6.02-1.06-1.65-1.06-4.34 0-6 2.45-3.82 6-6.02 9.75-6.02s7.3 2.2 9.74 6.02c1.06 1.65 1.06 4.34 0 6-2.44 3.82-5.99 6.02-9.74 6.02Zm0-16.54c-3.23 0-6.32 1.94-8.48 5.33-.75 1.17-.75 3.21 0 4.38 2.16 3.39 5.25 5.33 8.48 5.33 3.23 0 6.32-1.94 8.48-5.33.75-1.17.75-3.21 0-4.38-2.16-3.39-5.25-5.33-8.48-5.33Z"
                            fill="#fff"
                          />
                        </svg>
                      </Button>
                    </div>
                  </div>

                  <div>
                    <p className="overflow-x-auto">
                      <span className="mt-[15px] block text-xs text-[#cdcdcd]">
                        Amount earned
                      </span>

                      <span className="block overflow-x-auto">
                        <span className="font-heading text-xl font-semibold">
                          {isShown
                            ? `₦${convertNumberToNaira(Number(specificPlan?.results.total_interest_earned), false) || 0}`
                            : '****'}
                        </span>
                      </span>
                    </p>
                  </div>
                </div>

                <div className='w-full flex items-center justify-center py-[10px] gap-5 bg-[#0000001A] rounded-10'>
                  <Button className='text-sm' variant="unstyled" disabled>
                    Withdraw
                  </Button>
                  <svg fill="none" height="23" viewBox="0 0 1 23" width="1" xmlns="http://www.w3.org/2000/svg">
                    <line stroke="#E4E4E4" strokeWidth="0.3" x1="0.15" x2="0.149999" y1="6.55671e-09" y2="23" />
                  </svg>
                  <Button className='text-sm' variant="unstyled" disabled>
                    Rollover
                  </Button>
                </div>
              </div>
              <div className='w-full relative mt-3 flex items-center gap-[17px]'>
                <p className='text-[8px] '>Maturity date: <span className='ml-[5px] text-[#073D9F] text-xs font-bold'>{formatDateString(String(specificPlan?.results.maturity_date))}</span></p>
              </div>
            </div>
          </div>



          <div className='bg-white mt-4 relative'>
            {children}
          </div>



          <ButtonNavigation />

        </div>


      </main >
    </>
  );
}
