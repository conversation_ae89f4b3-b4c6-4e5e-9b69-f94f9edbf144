'use client';

import React from 'react';
import { z } from 'zod';

import {
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/core';
import { useVirtualWallet } from '../../api/getVirtualWallet';
import { launchNotification } from '@/utils';
import { useClipboard } from '@/hooks';



interface AddFundsModalProps {
  isAddFundsModalOpen: boolean;
  setAddFundsModalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  emailAddress: string;
}

const AddFundsModalSchema = z.object({
  AddFundsModal: z
    .string({ required_error: 'Please select a wallet type.' })
    .trim()
    .min(1, { message: 'Please select a wallet type.' }),
});

export type AddFundsModalValues = z.infer<typeof AddFundsModalSchema>;

export function AddFundsModal({
  isAddFundsModalOpen,
  setAddFundsModalState,
  heading,
  emailAddress
}: AddFundsModalProps) {
  const clipboard = useClipboard({ timeout: 500 });

  const { data } = useVirtualWallet(emailAddress as string);

  return (
    <Dialog open={isAddFundsModalOpen} onOpenChange={setAddFundsModalState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="text-left">
          <p className='text-xs font-semibold'>Fund wallet with transfer</p>

          <div className='space-y-2 mt-9 px-3 border-dashed border-[0.2px] border-[#9F9F9F] p-4 rounded-10'>
            <p className='text-xs font-semibold border-[#E9EBEE] border-b-[0.5px] pb-[10px]'>Fund wallet with any of the underlisted options</p>

            <div className='grid grid-cols-2 justify-between gap-4'>
              <div>
                <p className='text-xs text-[#556575] font-normal'>Account name</p>
                <p className='text-black text-sm font-semibold'>{data?.data.account_name}</p>
              </div>

              <div>
                <p className='text-xs text-[#556575] font-normal'>Account no</p>
                <div className='flex  gap-4'>
                  <p className='text-black text-sm font-semibold'>{data?.data.account_number}</p>

                  <button
                    onClick={() => {
                      clipboard.copy(String(data?.data.account_number));
                      launchNotification('neutral', 'Account number copied');
                    }}>
                    <svg className='cursor-pointer' fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8.325 16.5625H5.175C3.77119 16.5625 2.86908 16.2488 2.31012 15.6899C1.75115 15.1309 1.4375 14.2288 1.4375 12.825V9.675C1.4375 8.27119 1.75115 7.36908 2.31012 6.81012C2.86908 6.25115 3.77119 5.9375 5.175 5.9375H8.325C9.72881 5.9375 10.6309 6.25115 11.1899 6.81012C11.7488 7.36908 12.0625 8.27119 12.0625 9.675V12.825C12.0625 14.2288 11.7488 15.1309 11.1899 15.6899C10.6309 16.2488 9.72881 16.5625 8.325 16.5625ZM5.175 6.0625C3.98295 6.0625 3.02841 6.25824 2.39332 6.89332C1.75824 7.52841 1.5625 8.48295 1.5625 9.675V12.825C1.5625 14.017 1.75824 14.9716 2.39332 15.6067C3.02841 16.2418 3.98295 16.4375 5.175 16.4375H8.325C9.51705 16.4375 10.4716 16.2418 11.1067 15.6067C11.7418 14.9716 11.9375 14.017 11.9375 12.825V9.675C11.9375 8.48295 11.7418 7.52841 11.1067 6.89332C10.4716 6.25824 9.51705 6.0625 8.325 6.0625H5.175Z" fill="#032282" stroke="#032282" />
                      <path d="M12.0625 11.4375V11.9375H12.5625H12.825C14.017 11.9375 14.9716 11.7418 15.6067 11.1067C16.2418 10.4716 16.4375 9.51705 16.4375 8.325V5.175C16.4375 3.98295 16.2418 3.02841 15.6067 2.39332C14.9716 1.75824 14.017 1.5625 12.825 1.5625H9.675C8.48295 1.5625 7.52841 1.75824 6.89332 2.39332C6.25824 3.02841 6.0625 3.98295 6.0625 5.175V5.4375V5.9375H6.5625H8.325C9.72881 5.9375 10.6309 6.25115 11.1899 6.81012C11.7488 7.36908 12.0625 8.27119 12.0625 9.675V11.4375ZM12.825 12.0625H12C11.9869 12.0625 11.9713 12.0573 11.957 12.043C11.9427 12.0287 11.9375 12.0131 11.9375 12V9.675C11.9375 8.48295 11.7418 7.52841 11.1067 6.89332C10.4716 6.25824 9.51705 6.0625 8.325 6.0625H6C5.98694 6.0625 5.97128 6.0573 5.95699 6.04301C5.9427 6.02872 5.9375 6.01306 5.9375 6V5.175C5.9375 3.77119 6.25115 2.86908 6.81012 2.31012C7.36908 1.75115 8.27119 1.4375 9.675 1.4375H12.825C14.2288 1.4375 15.1309 1.75115 15.6899 2.31012C16.2488 2.86908 16.5625 3.77119 16.5625 5.175V8.325C16.5625 9.72881 16.2488 10.6309 15.6899 11.1899C15.1309 11.7488 14.2288 12.0625 12.825 12.0625Z" fill="#032282" stroke="#032282" />
                    </svg>
                  </button>
                </div>
              </div>

              <div>
                <p className='text-xs text-[#556575] font-normal'>Bank name</p>
                <p className='text-black text-sm font-semibold whitespace-nowrap'>{data?.data.bank_name}</p>
              </div>
            </div>
          </div>
        </DialogBody>
      </DialogContent>

    </Dialog>
  );
}
