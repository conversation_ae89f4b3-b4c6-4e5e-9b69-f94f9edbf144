import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';

import { OnboardingPageWrapper } from '../../misc/components';
import { BVNForm } from './misc/components';

export default function BVN({
  searchParams,
}: {
  searchParams: { email: string, phone: string };
}) {
  const { phone, email } = searchParams;

  return (
    <>
      <div className="mx-auto max-w-[32.375rem] px-2 md:hidden">
        <p className="relative mb-2 ml-auto w-max text-white md:hidden">5/7</p>
      </div>

      <OnboardingPageWrapper
        heading="Few more steps!"
        subHeading={
          <>
            Kindly fill the details below to create your{' '}
            <span className="font-bold">Seeds</span> account.
          </>
        }
      >
        <fieldset className="relative">
          <legend className="mb-1 text-xl font-semibold text-white">BVN</legend>
          <p className="text-xxs text-white">
            <Balancer>
              We need your Bank Verification Number (BVN) to create your
              operational wallet account.
            </Balancer>
          </p>

          <BVNForm email={email} phone={phone} />
        </fieldset>
      </OnboardingPageWrapper>
    </>
  );
}
