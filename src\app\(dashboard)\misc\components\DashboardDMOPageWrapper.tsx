import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';
import { useRouter } from 'next/navigation';

import { cn } from '@/utils/classNames';
import { Button, LinkButton } from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { ButtonNavigation } from './ButtomNavigation';
import { AddFundsModal } from './modals/AddFundsModal';
import { WithdrawalModal } from './modals/WithdrawalModal';
import { useOverview } from '../api/overview';
import { NoPinError, useUser } from '@/app/(auth)/(onboarding)/misc/api';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { AxiosError } from 'axios';
import { useQueryClient } from 'react-query';
import { useAllInactivePlans } from '../api/getAllInactivePlans';
import { shortenNumber } from '@/utils/numbers';
import { SaveIntrestsOptions } from './modals/saveIntrestsOptions';
import { useCreatePlanOTP } from '../api/createPlan';
import { useWithdrawalAccount } from '../api/withdrawals/getWithdrawalAcount';
import { SetUpAccount } from './modals/forms/SetBankAccount';
import { useAccountCheck } from '../api/getAccount';
import { SetNextOfKin } from './modals/SetNextOfKinModal';
import { useAuth } from '@/contexts/authentication';

export function DashboardDMOPageWrapper({
  children,
  heading,
  isCentered = false,
}: {
  children: React.ReactNode;
  heading: React.ReactNode;
  isCentered?: boolean;
}) {
  const { state: isShown, toggle: toggleShow } = useBooleanStateControl(true);
  const {
    data: viewUserData,
    error,
    isSuccess,
    isError } = useUser();
  const { email, phone_number } = viewUserData?.user_data || {};

  const {
    setEmailAddress,
    emailAddress,
    setCreatePlanResponse,
    setPhoneNumber,
    interest_type_onboarding,
    plan_name_onboarding,
    target_onboarding,
    duration_onboarding } = useOnboardingPlan();

  const { mutate: createPlan } =
    useCreatePlanOTP(emailAddress as string);

  // CREATE PLAN FOR USERS JUST SIGNED UP
  React.useEffect(() => {
    if (interest_type_onboarding === null ||
      plan_name_onboarding === null ||
      target_onboarding === null ||
      duration_onboarding === null
    ) {
    } else (

      createPlan(
        {
          duration: Number(duration_onboarding),
          interest_type: String(interest_type_onboarding),
          name: String(plan_name_onboarding),
          target: Number(target_onboarding),
        },
        {
          onSuccess: (data) => {
            setCreatePlanResponse({
              duration: data.data.data.duration,
              id: data.data.data.id,
              name: data.data.data.name,
              quotation_id: data.data.data.quotation_id,
              target: data.data.data.target,
              interest_type: data.data.data.interest_type,
              maturity_date: data.data.data.maturity_date
            })
            useOnboardingPlan.getState().clear();
          },
        }
      )



    )
  }, [createPlan, duration_onboarding, interest_type_onboarding, plan_name_onboarding, setCreatePlanResponse, target_onboarding])

  const { data } = useAllInactivePlans(emailAddress as string);

  const router = useRouter();
  const queryClient = useQueryClient();

  const typedError = error as AxiosError<NoPinError>;

  const errorStatus = typedError?.response?.status;

  const missingPinResponse =
    'You do not have any pin yet. Please create a transaction pin with link below';

  const hasNoPin = typedError?.response?.data?.message === missingPinResponse;

  React.useEffect(() => {
    if (!isSuccess && hasNoPin) {
      queryClient.clear();

      router.push('/transaction-pin');
    } else if (errorStatus === 401) {
      router.push('/login');
    }
  }, [isError, errorStatus, hasNoPin, isSuccess, queryClient, router]);


  React.useEffect(() => {
    setEmailAddress(email as string)
    setPhoneNumber(phone_number as string)
  }, [email, phone_number, setEmailAddress, setPhoneNumber])

  const { data: user_overview } = useOverview(emailAddress as string)

  const { data: withdrawal_accounts, error: isWithdrawError } = useWithdrawalAccount(emailAddress as string);


  const { data: account_check, } = useAccountCheck(emailAddress as string);

  const { replace } = useRouter();
  const { authDispatch } = useAuth();

  const handleLogOut = () => {
    if (authDispatch) authDispatch({ type: 'LOGOUT' });
    queryClient.clear();
    replace('/');
  };


  const {
    state: isWithdrawalModalOpen,
    setState: setWithdrawalModalState,
    setTrue: openWithdrawalModal,
  } = useBooleanStateControl();

  const {
    state: isSaveIntrestOptionsOpen,
    setState: setSaveIntrestOptionsState,
    setTrue: openSaveIntrestOptions,
  } = useBooleanStateControl();

  const {
    state: isAddFundsModalOpen,
    setState: setAddFundsModalState,
    setTrue: openAddFundsModal,
  } = useBooleanStateControl();

  const {
    state: isSetNextOfKinOpen,
    setState: setNextOfKinState,
    setTrue: openSetNextOfKin,
  } = useBooleanStateControl();

  const {
    state: isSetUpWithdrawAccountOpen,
    setState: setSetUpWithdrawAccountState,
    setTrue: openSetUpWithdrawAccount,
  } = useBooleanStateControl();

  React.useEffect(() => {
    if (account_check?.data.next_of_kin === false) {
      openSetNextOfKin()
    }
  }, [account_check?.data.next_of_kin, openSetNextOfKin])

  const handleWithdraw = () => {
    if (
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      isWithdrawError?.response?.data?.message === "no withdrawal account has been set for this user") {
      openSetUpWithdrawAccount()
    } else {
      openWithdrawalModal()
    }
  }

  return (
    <>
      <main className="relative mx-auto max-w-[34.375rem]  md:h-auto md:min-h-0 md:w-full md:grow-0 md:overflow-y-auto ">
        <div className="relative md:h-[812px] py-5">
          <div
            className={cn(
              'absolute inset-0 rounded-[5px] bg-[#F1F8FF]'
            )}
          />
          <h1
            className={cn(
              'relative flex justify-between items-center gap-2 px-4 mb-1 font-clash text-xl font-semibold leading-[normal] text-[#032282]',
              isCentered && 'text-center'
            )}
          >
            <div className='flex justify-between items-center gap-2'>
              <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                <circle cx="16" cy="16" fill="#DFEEFF" r="16" />
                <path d="M20.8251 24.9581H11.1751C8.89175 24.9581 7.04175 23.0998 7.04175 20.8165V14.6415C7.04175 13.5081 7.74175 12.0831 8.64175 11.3831L13.1334 7.88315C14.4834 6.83315 16.6417 6.78315 18.0417 7.76648L23.1917 11.3748C24.1834 12.0665 24.9584 13.5498 24.9584 14.7581V20.8248C24.9584 23.0998 23.1084 24.9581 20.8251 24.9581ZM13.9001 8.86648L9.40841 12.3665C8.81675 12.8331 8.29175 13.8915 8.29175 14.6415V20.8165C8.29175 22.4081 9.58341 23.7081 11.1751 23.7081H20.8251C22.4167 23.7081 23.7084 22.4165 23.7084 20.8248V14.7581C23.7084 13.9581 23.1334 12.8498 22.4751 12.3998L17.3251 8.79148C16.3751 8.12482 14.8084 8.15815 13.9001 8.86648Z" fill="#032282" />
                <path d="M16 21.625C15.6583 21.625 15.375 21.3417 15.375 21V18.5C15.375 18.1583 15.6583 17.875 16 17.875C16.3417 17.875 16.625 18.1583 16.625 18.5V21C16.625 21.3417 16.3417 21.625 16 21.625Z" fill="#032282" />
              </svg>

              <Balancer className={cn(isCentered && 'text-center')}>
                {heading}
              </Balancer>
            </div>

            <Button className='p-0' variant='unstyled' onClick={handleLogOut}>
              <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                <circle cx="16" cy="16" fill="#FF0000" fillOpacity="0.1" r="16" />
                <path d="M18.7001 24.5586H18.5918C14.8918 24.5586 13.1084 23.1002 12.8001 19.8336C12.7668 19.4919 13.0168 19.1836 13.3668 19.1502C13.7001 19.1169 14.0168 19.3752 14.0501 19.7169C14.2918 22.3336 15.5251 23.3086 18.6001 23.3086H18.7084C22.1001 23.3086 23.3001 22.1086 23.3001 18.7169V13.2836C23.3001 9.89189 22.1001 8.69189 18.7084 8.69189H18.6001C15.5084 8.69189 14.2751 9.68356 14.0501 12.3502C14.0084 12.6919 13.7168 12.9502 13.3668 12.9169C13.0168 12.8919 12.7668 12.5836 12.7918 12.2419C13.0751 8.92523 14.8668 7.44189 18.5918 7.44189H18.7001C22.7918 7.44189 24.5418 9.19189 24.5418 13.2836V18.7169C24.5418 22.8086 22.7918 24.5586 18.7001 24.5586Z" fill="#FF0000" />
                <path d="M18.4999 16.625H9.0166C8.67493 16.625 8.3916 16.3417 8.3916 16C8.3916 15.6583 8.67493 15.375 9.0166 15.375H18.4999C18.8416 15.375 19.1249 15.6583 19.1249 16C19.1249 16.3417 18.8416 16.625 18.4999 16.625Z" fill="#FF0000" />
                <path d="M10.875 19.4167C10.7167 19.4167 10.5584 19.3584 10.4334 19.2334L7.6417 16.4417C7.40003 16.2 7.40003 15.8 7.6417 15.5584L10.4334 12.7667C10.675 12.525 11.075 12.525 11.3167 12.7667C11.5584 13.0084 11.5584 13.4084 11.3167 13.65L8.9667 16L11.3167 18.35C11.5584 18.5917 11.5584 18.9917 11.3167 19.2334C11.2 19.3584 11.0334 19.4167 10.875 19.4167Z" fill="#FF0000" />
              </svg>

            </Button>

          </h1>
          <div className='px-4'>

            <div className='relative rounded-10 bg-white p-4 mt-5'>
              <div
                className={cn(
                  "flex min-w-0 grow flex-col justify-between rounded-xl bg-[#032282] bg-[url('/images/diagonal-triangles.svg')] bg-contain bg-right bg-no-repeat p-6 text-white sm:p-3 md:p-6"
                )}
              >
                <div className="mb-3 flex items-end justify-between gap-2 md:gap-6">
                  <div>
                    <div className='flex items-end gap-[50px]'>
                      <p className="overflow-x-auto">
                        <span className="mb-0.5 block text-xs text-[#cdcdcd]">
                          Commission wallet
                        </span>

                        <span className="block overflow-x-auto">
                          <span className="font-heading text-xl font-semibold">
                            {isShown
                              ? `₦${shortenNumber(Number(user_overview?.data?.main_wallet_balance)) || 0}`
                              : '****'}
                          </span>
                        </span>
                      </p>

                      <Button
                        className="shrink-0 pb-0.5"
                        size="unstyled"
                        variant="unstyled"
                        onClick={toggleShow}
                      >
                        <span className="sr-only">
                          {isShown ? 'Hide balance' : 'Show balance'}
                        </span>
                        <svg
                          fill="none"
                          height={24}
                          viewBox="0 0 24 24"
                          width={24}
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 16.33c-2.39 0-4.33-1.94-4.33-4.33S9.61 7.67 12 7.67s4.33 1.94 4.33 4.33-1.94 4.33-4.33 4.33Zm0-7.16c-1.56 0-2.83 1.27-2.83 2.83s1.27 2.83 2.83 2.83 2.83-1.27 2.83-2.83S13.56 9.17 12 9.17Z"
                            fill="#fff"
                          />
                          <path
                            d="M12 21.02c-3.76 0-7.31-2.2-9.75-6.02-1.06-1.65-1.06-4.34 0-6 2.45-3.82 6-6.02 9.75-6.02s7.3 2.2 9.74 6.02c1.06 1.65 1.06 4.34 0 6-2.44 3.82-5.99 6.02-9.74 6.02Zm0-16.54c-3.23 0-6.32 1.94-8.48 5.33-.75 1.17-.75 3.21 0 4.38 2.16 3.39 5.25 5.33 8.48 5.33 3.23 0 6.32-1.94 8.48-5.33.75-1.17.75-3.21 0-4.38-2.16-3.39-5.25-5.33-8.48-5.33Z"
                            fill="#fff"
                          />
                        </svg>
                      </Button>
                    </div>
                    <div className='flex items-center gap-[37px]'>
                      <div>
                        <p className="overflow-x-auto">
                          <span className="mt-[15px] block text-xs text-[#cdcdcd]">
                            Total portfolio
                          </span>

                          <span className="block overflow-x-auto">
                            <span className="font-heading text-xl font-semibold">
                              {isShown
                                ? `₦${shortenNumber(Number(user_overview?.data?.total_balance)) || 0}`
                                : '****'}
                            </span>
                          </span>
                        </p>
                      </div>

                      <div>
                        <p className="overflow-x-auto">
                          <span className="mt-[15px] block text-xs text-[#cdcdcd]">
                            Referrals
                          </span>

                          <span className="block overflow-x-auto">
                            <span className="font-heading text-xl font-semibold">
                              {isShown
                                ? `₦${shortenNumber(Number(user_overview?.data?.total_balance)) || 0}`
                                : '****'}
                            </span>
                          </span>
                        </p>
                      </div>

                    </div>
                  </div>

                  <div className='space-y-[15px]'>
                    <div>
                      <Button className='px-[19px] py-[11px] bg-[#0000001A]' onClick={handleWithdraw}>
                        Withdraw
                      </Button>
                    </div>
                    <div>
                      <Button className='px-[19px] py-[11px] bg-[#0000001A]' onClick={openAddFundsModal}>
                        Add fund
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className='w-full relative flex items-center gap-[17px] py-3 px-6'>
            <LinkButton className='bg-white rounded-[7px] py-[11px] px-4 flex gap-3 items-center' href="/loan-requests" size={'fullWidthAlign'} >
              <svg className='hidden md:block' fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_17032_14391)">
                  <circle cx="16" cy="14" fill="url(#paint0_linear_17032_14391)" r="11" />
                </g>
                <path d="M16.4696 13.531V10.2383H15.5288V13.531H12.2361V14.4718H15.5288V17.7646H16.4696V14.4718H19.7624V13.531H16.4696Z" fill="#FDFDFD" />
                <defs>
                  <filter colorInterpolationFilters="sRGB" filterUnits="userSpaceOnUse" height="32" id="filter0_d_17032_14391" width="32" x="0" y="0">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
                    <feOffset dy="2" />
                    <feGaussianBlur stdDeviation="2.5" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0.305882 0 0 0 0 0.305882 0 0 0 0 0.305882 0 0 0 0.2 0" />
                    <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_17032_14391" />
                    <feBlend in="SourceGraphic" in2="effect1_dropShadow_17032_14391" mode="normal" result="shape" />
                  </filter>
                  <linearGradient gradientUnits="userSpaceOnUse" id="paint0_linear_17032_14391" x1="5" x2="27" y1="3" y2="25">
                    <stop stopColor="#0D6ED2" />
                    <stop offset="1" stopColor="#073EA0" />
                  </linearGradient>
                </defs>
              </svg>
              <p className='text-[#052F8F] text-sm leading-5 font-semibold font-sans'>
                New onlending
              </p>
            </LinkButton>
            <LinkButton className='bg-white rounded-[7px] py-[11px] px-4 flex gap-3 items-center' href="/loan-requests" size={'fullWidthAlign'}>
              <svg className='hidden md:block' fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg"
              >
                <g filter="url(#filter0_d_17329_9493)">
                  <circle cx="16" cy="14" fill="url(#paint0_linear_17329_9493)" r="11" />
                </g>
                <path d="M18.5 18.375H14.25C14.045 18.375 13.875 18.205 13.875 18C13.875 17.795 14.045 17.625 14.25 17.625H18.5C19.93 17.625 20.625 16.93 20.625 15.5V12.5C20.625 11.07 19.93 10.375 18.5 10.375H13.5C12.07 10.375 11.375 11.07 11.375 12.5V15.6C11.375 15.805 11.205 15.975 11 15.975C10.795 15.975 10.625 15.805 10.625 15.6V12.5C10.625 10.675 11.675 9.625 13.5 9.625H18.5C20.325 9.625 21.375 10.675 21.375 12.5V15.5C21.375 17.325 20.325 18.375 18.5 18.375Z" fill="white" />
                <path d="M16 15.625C15.105 15.625 14.375 14.895 14.375 14C14.375 13.105 15.105 12.375 16 12.375C16.895 12.375 17.625 13.105 17.625 14C17.625 14.895 16.895 15.625 16 15.625ZM16 13.125C15.52 13.125 15.125 13.52 15.125 14C15.125 14.48 15.52 14.875 16 14.875C16.48 14.875 16.875 14.48 16.875 14C16.875 13.52 16.48 13.125 16 13.125Z" fill="white" />
                <path d="M19.25 15.625C19.045 15.625 18.875 15.455 18.875 15.25V12.75C18.875 12.545 19.045 12.375 19.25 12.375C19.455 12.375 19.625 12.545 19.625 12.75V15.25C19.625 15.455 19.455 15.625 19.25 15.625Z" fill="white" />
                <path d="M12.5 19.375C11.67 19.375 10.89 18.935 10.47 18.22C10.245 17.86 10.125 17.435 10.125 17C10.125 15.69 11.19 14.625 12.5 14.625C13.81 14.625 14.875 15.69 14.875 17C14.875 17.435 14.755 17.86 14.53 18.225C14.11 18.935 13.33 19.375 12.5 19.375ZM12.5 15.375C11.605 15.375 10.875 16.105 10.875 17C10.875 17.295 10.955 17.585 11.11 17.835C11.4 18.325 11.935 18.625 12.5 18.625C13.065 18.625 13.6 18.325 13.89 17.84C14.045 17.585 14.125 17.3 14.125 17C14.125 16.105 13.395 15.375 12.5 15.375Z" fill="white" />
                <path d="M11.125 18.7502C11.03 18.7502 10.935 18.7152 10.86 18.6402C10.715 18.4952 10.715 18.2552 10.86 18.1102L13.61 15.3602C13.755 15.2152 13.995 15.2152 14.14 15.3602C14.285 15.5052 14.285 15.7452 14.14 15.8902L11.39 18.6402C11.315 18.7152 11.22 18.7502 11.125 18.7502Z" fill="white" />
                <defs>
                  <filter colorInterpolationFilters="sRGB" filterUnits="userSpaceOnUse" height="32" id="filter0_d_17329_9493" width="32" x="0" y="0">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
                    <feOffset dy="2" />
                    <feGaussianBlur stdDeviation="2.5" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0.305882 0 0 0 0 0.305882 0 0 0 0 0.305882 0 0 0 0.2 0" />
                    <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_17329_9493" />
                    <feBlend in="SourceGraphic" in2="effect1_dropShadow_17329_9493" mode="normal" result="shape" />
                  </filter>
                  <linearGradient gradientUnits="userSpaceOnUse" id="paint0_linear_17329_9493" x1="5" x2="27" y1="3" y2="25">
                    <stop stopColor="#0D6ED2" />
                    <stop offset="1" stopColor="#073EA0" />
                  </linearGradient>
                </defs>
              </svg>

              <p className='text-[#052F8F] text-sm leading-5 font-semibold font-sans'>
                Loan requests
              </p>
            </LinkButton>
          </div>

          <div className='relative w-full mt-2 mb-4 px-6 '>
            {account_check?.data.next_of_kin === false ? (

              <div className='flex bg-white items-center justify-between w-full py-[9px] px-4 rounded-lg'>
                <div className="flex gap-2 items-center">
                  <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#212121" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                    <path d="M12 8V13" stroke="#212121" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                    <path d="M11.9951 16H12.0041" stroke="#212121" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" />
                  </svg>
                  <p className='font-medium text-[#646464] text-right text-sm'>
                    fill next of kin details</p>
                </div>

                <LinkButton
                  className="bg-[#F1F8FF] py-[8px] px-[10px] text-xxs text-[#032282]"
                  href={`sign-up/next-of-kin?email=${email}&phone=${phone_number}`}
                >
                  Continue
                </LinkButton>
              </div>

            ) : (<>
            </>)}
          </div>

          <div className='flex relative items-center flex-col justify-between w-full mt-2 mb-4 px-6 '>
            {data?.results.length === 0 ? (<>
              <p className='font-medium text-[#646464] text-right text-sm'>No inactive plans</p>
            </>) : (<>

              <>
                <div className='flex bg-white items-center justify-between w-full py-[9px] px-4 rounded-lg'>
                  <div className='flex gap-[10px] '>
                    <svg fill="none" height="34" viewBox="0 0 34 34" width="34" xmlns="http://www.w3.org/2000/svg">
                      <rect fill="#E9FFF0" height="34" rx="17" width="34" />
                      <path d="M22 15.9582C21.6583 15.9582 21.375 15.6748 21.375 15.3332V13.6665C21.375 11.0415 20.6333 9.2915 17 9.2915C13.3667 9.2915 12.625 11.0415 12.625 13.6665V15.3332C12.625 15.6748 12.3417 15.9582 12 15.9582C11.6583 15.9582 11.375 15.6748 11.375 15.3332V13.6665C11.375 11.2498 11.9583 8.0415 17 8.0415C22.0417 8.0415 22.625 11.2498 22.625 13.6665V15.3332C22.625 15.6748 22.3417 15.9582 22 15.9582Z" fill="#01AE53" />
                      <path d="M21.1667 25.9585H12.8334C9.15841 25.9585 8.04175 24.8418 8.04175 21.1668V19.5002C8.04175 15.8252 9.15841 14.7085 12.8334 14.7085H21.1667C24.8417 14.7085 25.9584 15.8252 25.9584 19.5002V21.1668C25.9584 24.8418 24.8417 25.9585 21.1667 25.9585ZM12.8334 15.9585C9.85008 15.9585 9.29175 16.5252 9.29175 19.5002V21.1668C9.29175 24.1418 9.85008 24.7085 12.8334 24.7085H21.1667C24.1501 24.7085 24.7084 24.1418 24.7084 21.1668V19.5002C24.7084 16.5252 24.1501 15.9585 21.1667 15.9585H12.8334Z" fill="#01AE53" />
                      <path d="M13.6666 21.1667C13.5583 21.1667 13.4499 21.1417 13.3499 21.1001C13.2416 21.0584 13.1583 21 13.0749 20.925C12.9249 20.7667 12.8333 20.5584 12.8333 20.3334C12.8333 20.2251 12.8582 20.1167 12.8999 20.0167C12.9416 19.9084 12.9999 19.8251 13.0749 19.7417C13.1583 19.6667 13.2416 19.6084 13.3499 19.5667C13.6499 19.4334 14.0249 19.5084 14.2582 19.7417C14.3332 19.8251 14.3916 19.9167 14.4333 20.0167C14.4749 20.1167 14.4999 20.2251 14.4999 20.3334C14.4999 20.5501 14.4082 20.7667 14.2582 20.925C14.0999 21.075 13.8833 21.1667 13.6666 21.1667Z" fill="#01AE53" />
                      <path d="M17.0001 21.1669C16.7834 21.1669 16.5668 21.0752 16.4084 20.9252C16.2584 20.7669 16.1667 20.5586 16.1667 20.3336C16.1667 20.2253 16.1834 20.1169 16.2334 20.0169C16.2751 19.9169 16.3334 19.8253 16.4084 19.7419C16.6001 19.5503 16.8918 19.4586 17.1584 19.5169C17.2168 19.5252 17.2668 19.5419 17.3168 19.5669C17.3668 19.5836 17.4167 19.6086 17.4667 19.6419C17.5084 19.6669 17.5501 19.7086 17.5917 19.7419C17.6667 19.8253 17.7251 19.9169 17.7668 20.0169C17.8084 20.1169 17.8334 20.2253 17.8334 20.3336C17.8334 20.5586 17.7417 20.7669 17.5917 20.9252C17.5501 20.9586 17.5084 20.9919 17.4667 21.0252C17.4167 21.0586 17.3668 21.0836 17.3168 21.1003C17.2668 21.1253 17.2168 21.1419 17.1584 21.1503C17.1084 21.1586 17.0501 21.1669 17.0001 21.1669Z" fill="#01AE53" />
                      <path d="M20.3333 21.1669C20.1083 21.1669 19.9 21.0752 19.7417 20.9252C19.6667 20.8419 19.6083 20.7503 19.5667 20.6503C19.525 20.5503 19.5 20.4419 19.5 20.3336C19.5 20.1169 19.5917 19.9003 19.7417 19.7419C19.7833 19.7086 19.825 19.6753 19.8667 19.6419C19.9167 19.6086 19.9667 19.5836 20.0167 19.5669C20.0667 19.5419 20.1167 19.5252 20.1667 19.5169C20.4417 19.4586 20.725 19.5503 20.925 19.7419C21.075 19.9003 21.1667 20.1086 21.1667 20.3336C21.1667 20.4419 21.1417 20.5503 21.1 20.6503C21.0583 20.7586 21 20.8419 20.925 20.9252C20.7667 21.0752 20.55 21.1669 20.3333 21.1669Z" fill="#01AE53" />
                    </svg>

                    <div className=''>
                      <p className='text-[#242424] text-sm font-semibold'>{data?.results[0].name}</p>
                      <p className='text-xxs font-medium text-[#646464]'>Status - <span className='text-[#FF6B00]'>Inactive</span></p>
                    </div>
                  </div>

                  <Button className="bg-[#F1F8FF] py-[8px] px-[10px] text-xxs text-[#032282]" onClick={() => {
                    setCreatePlanResponse({
                      id: Number(data?.results[0].id),
                      name: String(data?.results[0].name),
                      duration: Number(data?.results[0].duration),
                      target: Number(data?.results[0].target),
                      quotation_id: String(data?.results[0].quotation_id),
                      interest_type: String(data?.results[0].interest_type),
                      maturity_date: String(data?.results[0].maturity_date)
                    })

                    openSaveIntrestOptions()
                  }}>Activate</Button>
                </div>

              </>
            </>)}
          </div>


          <div className='bg-white relative'>
            {children}
          </div>



          <ButtonNavigation />
          {/* <MobileMenuBar /> */}
        </div>


      </main >

      <WithdrawalModal
        account_details={withdrawal_accounts}
        heading='Withdrawal'
        isWithdrawalModalOpen={isWithdrawalModalOpen}
        setWithdrawalModalState={setWithdrawalModalState}
      />

      <SetNextOfKin
        email={String(emailAddress)}
        heading='Next of kin'
        isSetNextOfKinOpen={isSetNextOfKinOpen}
        phone_number={String(phone_number)}
        setNextOfKinState={setNextOfKinState}
      />

      <AddFundsModal
        emailAddress={String(emailAddress)}
        heading='Add fund'
        isAddFundsModalOpen={isAddFundsModalOpen}
        setAddFundsModalState={setAddFundsModalState}
      />

      <SetUpAccount
        emailAddress={String(emailAddress)}
        heading='Withdrawal'
        isSetUpAccountOpen={isSetUpWithdrawAccountOpen}
        setSetUpAccountState={setSetUpWithdrawAccountState}
      />

      <SaveIntrestsOptions
        email={String(emailAddress)}
        heading='Save'
        isSaveIntrestsOptionsOpen={isSaveIntrestOptionsOpen}
        setSaveIntrestsOptionsState={setSaveIntrestOptionsState}
      />

    </>
  );
}
