'use client';

import { useRouter } from 'next/navigation';
import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
import type { AxiosError } from 'axios';

import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';


import {
  Button,
  ErrorModal,
  FormError,
  Input,
  LoaderModal,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';

import { useSecurityQuestions } from '../api/getSecurityQuestions';
import { useAnswerSecurityQuestions } from '../api';
import { useOnboardingPlan } from '@/store/onBoardingPlan';

const SecurityQuestionsFormSchema = z.object({
  first_security_question: z
    .string({ required_error: 'Please select a question.' })
    .trim()
    .min(1, { message: 'Please select a question.' }),
  first_security_answer: z
    .string({ required_error: 'Please answer your first question.' })
    .trim()
    .min(1, { message: 'Please answer your first question.' }),

  second_security_question: z
    .string({ required_error: 'Please select a second question.' })
    .trim()
    .min(1, { message: 'Please select a second question.' }),
  second_security_answer: z
    .string({ required_error: 'Please answer your second question.' })
    .trim()
    .min(1, { message: 'Please answer your second question.' }),
});

export type SecurityQuestionsFormValues = z.infer<
  typeof SecurityQuestionsFormSchema
>;

interface SecurityQuestionsFormProps {
  phone: string;
  // securityQuestionsResponse: {
  //   questions: string[];
  // };
}

export function SecurityQuestionsForm({

}: SecurityQuestionsFormProps) {
  const router = useRouter();
  const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
    useBooleanStateControl();

  const { token_onboarding } = useOnboardingPlan();

  const { data: viewSecurityQuestions, isLoading: isSecurityQuestionsLoading } = useSecurityQuestions(String(token_onboarding));

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<SecurityQuestionsFormValues>({
    resolver: zodResolver(SecurityQuestionsFormSchema),
  });

  const {
    mutate: answerSecurityQuestions,
    isLoading: isAnswerSecurityQuestionsLoading,
  } = useAnswerSecurityQuestions(String(token_onboarding));

  const onCreatePasscodeSubmit = (
    submittedData: SecurityQuestionsFormValues
  ) => {
    answerSecurityQuestions(submittedData, {
      onSuccess: () => {
        openLoaderModal();
        router.push(`/sign-up/welcome`);
      },

      onError: (error: unknown) => {
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        openErrorModalWithMessage(errorMessage as string);
      },
    });
  };

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen || isSecurityQuestionsLoading} />

      <form
        className="relative"
        onSubmit={handleSubmit(onCreatePasscodeSubmit)}
      >
        <Label className="sr-only" htmlFor="question1">
          Question 1
        </Label>
        <Controller
          control={control}
          name="first_security_question"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                iconClassName="fill-white"
                id="question1"
                ref={ref}
              >
                <SelectValue placeholder="Question 1" />
              </SelectTrigger>
              <SelectContent>
                {viewSecurityQuestions?.questions?.map(question => {
                  return (
                    <SelectItem key={question} value={question}>
                      {question}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.first_security_question && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.first_security_question.message}
          />
        )}

        <Label className="sr-only" htmlFor="answer1">
          Answer 1
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="answer1"
          placeholder="Answer 1"
          type="text"
          {...register('first_security_answer')}
        />
        {errors?.first_security_answer && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.first_security_answer.message}
          />
        )}

        <Label className="sr-only" htmlFor="question2">
          Question 2
        </Label>
        <Controller
          control={control}
          name="second_security_question"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-6 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                iconClassName="fill-white"
                id="question2"
                ref={ref}
              >
                <SelectValue placeholder="Question 2" />
              </SelectTrigger>
              <SelectContent>
                {viewSecurityQuestions?.questions?.map(question => {
                  return (
                    <SelectItem key={question} value={question}>
                      {question}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.second_security_question && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.second_security_question.message}
          />
        )}

        <Label className="sr-only" htmlFor="answer1">
          Answer 2
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="answer2"
          placeholder="Answer 2"
          type="text"
          {...register('second_security_answer')}
        />
        {errors?.second_security_answer && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.second_security_answer.message}
          />
        )}

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isAnswerSecurityQuestionsLoading}
          type="submit"
          variant="white"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>
              {isAnswerSecurityQuestionsLoading ? 'Loading' : 'Continue'}
            </span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
      </form >

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
