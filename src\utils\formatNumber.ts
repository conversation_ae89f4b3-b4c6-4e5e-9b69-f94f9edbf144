/**
 * Formats a number string by adding commas as thousand separators
 * @param value - The number string to format
 * @returns The formatted number string with commas
 */
export const formatNumberWithCommas = (value: string): string => {
    // Remove any existing commas
    const cleanValue = value.replace(/,/g, '');
    
    // Add commas for thousands
    return cleanValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}; 