import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface CreateAccountDTO {
    account_number: string;
    bank_name: string;
    bank_code: string;
    account_name: string;
    transaction_pin: string
}

export interface CreatePlanOTPResponse {
  message: string;
}

const createAccount = (createAccountDTO: CreateAccountDTO,  email: string): Promise<AxiosResponse> => {
  const headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  
  
  return savingsAxios.post(`/payment/withdrawal-account/`, createAccountDTO, {headers});
};

export const useCreateAccount = (email: string) => {
  return useMutation('createAccount', (createAccountDTO: CreateAccountDTO) => createAccount(createAccountDTO, email), {});
};
