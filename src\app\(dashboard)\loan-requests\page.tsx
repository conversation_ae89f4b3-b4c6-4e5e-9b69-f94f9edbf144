'use client'


import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useLoanRequests } from '../misc/api/getLoanRequeusts';
import { LoanRequestPageWrapper } from '../misc/components/LoanRequestPageWrapper';
import { LoaderModal, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/core';
import { addCommasToNumber } from '@/utils';

const NetworkTypeOptions = [
    { name: 'Today', value: 'today' },
    { name: 'This week', value: 'this week' },
];

export default function LoanRequests() {
    const { emailAddress } = useOnboardingPlan();

    const { data: loan_request, isLoading } = useLoanRequests(emailAddress as string);

    return (
        <>
            <LoaderModal
                isOpen={
                    isLoading
                }
            />


            <LoanRequestPageWrapper heading={"Loan requests"}>
                <div className='p-6  h-[500px] md:h-[600px] overflow-auto'>
                    <div className='w-full flex justify-between items-center'>
                        <p className='text-[#242424] font-normal'>Pending loan requests</p>
                        <div className=''>
                            <Select >
                                <SelectTrigger className="bg-[#F1F8FF] text-[#032282]" id="network">
                                    <SelectValue className='text-[#032282]' placeholder="Sort by" />
                                </SelectTrigger>
                                <SelectContent className="text-[#032282]">
                                    {NetworkTypeOptions.map(({ name, value }) => {
                                        return (
                                            <SelectItem key={name} value={value}>
                                                {name}
                                            </SelectItem>
                                        );
                                    })}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>


                    {loan_request?.data?.map(({ borrowers_name, loan_amount, borrowers_ministry }, index) => {
                        return (
                            <>
                                <div className='mt-8' key={index}>
                                    <div className='flex items-center justify-between w-full mt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]'>
                                        <div className='flex gap-[10px] '>
                                            <svg fill="none" height="34" viewBox="0 0 34 34" width="34" xmlns="http://www.w3.org/2000/svg">
                                                <rect fill="#F1F8FF" height="34" rx="17" width="34" />
                                                <path d="M17.1329 16.6837C17.1079 16.6837 17.0912 16.6837 17.0662 16.6837C17.0246 16.6753 16.9662 16.6753 16.9162 16.6837C14.4996 16.6087 12.6746 14.7087 12.6746 12.367C12.6746 9.98366 14.6162 8.04199 16.9996 8.04199C19.3829 8.04199 21.3246 9.98366 21.3246 12.367C21.3162 14.7087 19.4829 16.6087 17.1579 16.6837C17.1496 16.6837 17.1412 16.6837 17.1329 16.6837ZM16.9996 9.29199C15.3079 9.29199 13.9246 10.6753 13.9246 12.367C13.9246 14.0337 15.2246 15.3753 16.8829 15.4337C16.9246 15.4253 17.0412 15.4253 17.1496 15.4337C18.7829 15.3587 20.0662 14.017 20.0746 12.367C20.0746 10.6753 18.6912 9.29199 16.9996 9.29199Z" fill="#032282" />
                                                <path d="M17.1413 25.7913C15.508 25.7913 13.8663 25.3747 12.6247 24.5413C11.4663 23.7747 10.833 22.7247 10.833 21.583C10.833 20.4413 11.4663 19.383 12.6247 18.608C15.1247 16.9497 19.1747 16.9497 21.658 18.608C22.808 19.3747 23.4497 20.4247 23.4497 21.5663C23.4497 22.708 22.8163 23.7663 21.658 24.5413C20.408 25.3747 18.7747 25.7913 17.1413 25.7913ZM13.3163 19.658C12.5163 20.1913 12.083 20.8747 12.083 21.5913C12.083 22.2997 12.5247 22.983 13.3163 23.508C15.3913 24.8997 18.8913 24.8997 20.9663 23.508C21.7663 22.9747 22.1997 22.2913 22.1997 21.5747C22.1997 20.8663 21.758 20.183 20.9663 19.658C18.8913 18.2747 15.3913 18.2747 13.3163 19.658Z" fill="#032282" />
                                            </svg>


                                            <div>
                                                <p className='text-[#242424] text-sm font-semibold'>{borrowers_name}</p>
                                                <p className='text-xs font-medium text-[#646464]'>{borrowers_ministry}</p>
                                            </div>
                                        </div>

                                        <div>
                                            <p className='text-[#032282] text-sm font-semibold text-right'>-₦{addCommasToNumber(loan_amount)}</p>
                                            <p className='text-xs font-medium text-[#646464]'>12 months loan</p>
                                        </div>
                                    </div>



                                </div>
                            </>
                        )
                    })}

                </div>
            </LoanRequestPageWrapper>
        </>
    );
}
