'use client';

import * as React from 'react';


import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from '@radix-ui/react-tabs';
import { EmailLoginForm } from './EmailLogin';
import { PhoneNumberLoginForm } from './PhoneNumberLogin';

interface GetStartedProps {
  referral_code: string | null
}

export function NewLoginForm({ referral_code }: GetStartedProps) {

  return (
    <main className=''>

      <Tabs className="relative w-full" defaultValue="email">
        <TabsList className="w-full flex justify-start rounded-10 bg-transparent text-white font-medium pb-0 ">
          <TabsTrigger
            className=" w-full pb-2  border-b-[5px] border-b-white text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
            value="email"
          >
            Email login
          </TabsTrigger>
          <div>
            <svg fill="none" height="34" viewBox="0 0 3 34" width="3" xmlns="http://www.w3.org/2000/svg">
              <rect fill="white" height="3" rx="1.5" transform="rotate(90 3 0)" width="34" x="3" />
            </svg>
          </div>

          <TabsTrigger
            className=" w-full border-b-[5px] pb-2 border-b-white  text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
            value="phone"
          >
            Phone no login
          </TabsTrigger>
        </TabsList>

        <TabsContent
          className="mt-[18px] rounded-10"
          value="email"
        >
          <EmailLoginForm
            referral_code={referral_code}
          />
        </TabsContent>

        <TabsContent
          className="mt-[18px] rounded-10"
          value="phone"
        >
          <PhoneNumberLoginForm
            referral_code={referral_code} />
        </TabsContent>
      </Tabs>
    </main>
  );
}
