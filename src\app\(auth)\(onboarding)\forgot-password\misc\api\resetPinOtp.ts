import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface requestResetOtpDTO {
    phone_number: string;
    otp: string;
}

const requestResetPinOtp = (requestResetOtpDTO: requestResetOtpDTO): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/second_reset_confirm_login_pin/`, requestResetOtpDTO);
};

export const useRequestResetPinOtp = () => {
  return useMutation('request-reset-pin', requestResetPinOtp, {});
};