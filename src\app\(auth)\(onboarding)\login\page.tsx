'use client'

import * as React from 'react';

import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { OnboardingPageWrapper } from '../misc/components';
import { NewLoginForm } from './misc/components';

// import { useSearchParams } from 'next/navigation'

export default function Login() {
  // const searchParams = useSearchParams();
  // const referral_code = searchParams.get("referral_code");
  const referral_code = ''

  const { setReferallCodeOnboading } = useOnboardingPlan();

  React.useEffect(() => {
    if (referral_code === null) {

    } else {
      setReferallCodeOnboading(referral_code)
    }
  }, [referral_code, setReferallCodeOnboading])

  return (
    <>

      <OnboardingPageWrapper
        heading="Welcome back 👋"
        subHeading="Login as an Onlender or micro saver by entering your 
details below."
      >
        <NewLoginForm
          referral_code={referral_code}
        />
      </OnboardingPageWrapper>
    </>
  );
}
