import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface OverviewData {
  status: boolean
  data: OverviewEntity
}

export interface OverviewEntity {
  main_wallet_balance: number
  total_balance: number
  total_savings: number
}


export const getOverview = async (email: string): Promise<OverviewData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get('/onlending/summary/', {headers});
  return data;
};

export const useOverview = (email: string) =>
  useQuery('on-lending-overview', () => getOverview(email), );
 