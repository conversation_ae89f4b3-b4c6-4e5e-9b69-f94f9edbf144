// import { authAxiosClient } from '@/lib/apiClient';

// import { useMutation } from '@tanstack/react-query';

// export interface resetPasswordDTO {
//     pin1: string;
//     pin2: string;
//     phone_number: string;
//     otp_value: string;
// }

// const resetPassword = async (resetPasswordDTO: resetPasswordDTO) => {
//     const response = await authAxiosClient.post(
//         '/agency/user/third_reset_create_login_pin/',
//         resetPasswordDTO
//     );
//     return response.data;
// };

// export const useResetPassword = () => {
//     return useMutation({
//         mutationFn: resetPassword,
//     });
// };

import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface resetPasswordDTO {
    pin1: string;
    pin2: string;
    phone_number: string;
    otp_value: string;
}

const resetPassword = (resetPasswordDTO: resetPasswordDTO): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/third_reset_create_login_pin/`, resetPasswordDTO);
};

export const useResetPassword = () => {
  return useMutation('reset-password', resetPassword, {});
};