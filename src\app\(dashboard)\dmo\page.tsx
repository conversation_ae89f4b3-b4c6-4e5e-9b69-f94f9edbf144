'use client'

import * as React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/core';


import { cn } from '@/utils/classNames';
import { useOnboardingPlan } from '@/store/onBoardingPlan';

import { ActivePlans } from '../misc/components/plan-tabs/ActivePlans';
import { InActivePlans } from '../misc/components/plan-tabs/InActivePlans';
import { MaturedPlans } from '../misc/components/plan-tabs/MaturedPlans';
import { DashboardDMOPageWrapper } from '../misc/components/DashboardDMOPageWrapper';


export default function Login() {
    const { setPlanType, plan_type } = useOnboardingPlan();

    return (
        <>
            <DashboardDMOPageWrapper heading={"Onlending"}>
                <div className="relative h-[300px] md:h-[400px] overflow-auto px-6">
                    <Tabs className=" bg-white   py-[10px] w-full max-w-full" defaultValue={plan_type || "active"}>
                        <TabsList className="grid w-full gap-5 grid-cols-3 bg-[#F1F8FF]">
                            <TabsTrigger
                                className={cn(
                                    plan_type === 'active'
                                        ? 'data-[state=active]:bg-[#C7E0FF] data-[state=active]:font-bold data-[state=active]:text-[#032282] data-[state=active]:shadow-sm'
                                        : 'bg-transparent',
                                    'py-[9px] px-5 text-[#052F8F]  bg-transparent w-auto text-[13px] font-medium rounded-[8px]'
                                )}
                                value={`${plan_type}`}
                                onClick={() => {
                                    setPlanType("active")
                                    location.reload()
                                }}
                            >
                                <p>Active </p>
                            </TabsTrigger>

                            <TabsTrigger

                                className={cn(
                                    plan_type === 'mature'
                                        ? 'data-[state=active]:bg-[#C7E0FF] data-[state=active]:font-bold data-[state=active]:text-[#032282] data-[state=active]:shadow-sm'
                                        : 'bg-transparent',
                                    'py-[9px] px-5 text-[#052F8F]  bg-transparent w-auto text-[13px] font-medium rounded-[8px]'
                                )}
                                value={`${plan_type}`}
                                onClick={() => {
                                    setPlanType("mature")
                                    location.reload()
                                }}>
                                <p>Matured  </p>
                            </TabsTrigger>



                            <TabsTrigger
                                className={cn(
                                    plan_type === 'inactive'
                                        ? 'data-[state=active]:bg-[#C7E0FF] data-[state=active]:font-bold data-[state=active]:text-[#032282] data-[state=active]:shadow-sm'
                                        : 'bg-transparent',
                                    'py-[9px] px-5 text-[#052F8F]  bg-transparent w-auto text-[13px] font-medium rounded-[8px]'
                                )}
                                value={`${plan_type}`}
                                onClick={() => {
                                    setPlanType("inactive")
                                    location.reload()
                                }}
                            >

                                <p>Inactive</p>

                            </TabsTrigger>

                        </TabsList>

                        <TabsContent className="mt-2 w-full" value="active">
                            {/* TODO CREATE COMPONENT FOR ALL THIS  */}
                            <ActivePlans />
                        </TabsContent>

                        <TabsContent className="mt-2 w-full" value="mature">
                            {/* TODO CREATE COMPONENT FOR ALL THIS  */}
                            <MaturedPlans />

                        </TabsContent>

                        <TabsContent className="mt-2 w-full" value="inactive">
                            <InActivePlans />
                        </TabsContent>

                    </Tabs>
                </div>
            </DashboardDMOPageWrapper>
        </>
    );
}
