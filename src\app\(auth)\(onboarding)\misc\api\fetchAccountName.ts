import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';

import { z } from 'zod';

const FetchAccountNameResponseSchema = z.object({
  message: z.string(),
  data: z.object({ account_number: z.string(), account_name: z.string() }),
});

export type FetchAccountNameResponse = z.infer<
  typeof FetchAccountNameResponseSchema
>;

export interface FetchAccountNameDto {
  account_number: string;
  bank_code: string | undefined;
}
// const FetchAccountName = async (fetchAccountNameDto: FetchAccountNameDto) => {
//   const response = await sendMoneyManagementAxiosClient.post(
//     '/send/fetch_account_name/',
//     fetchAccountNameDto,
//     {
//       headers: {
//         'Content-Type': 'application/json',
//       },
//     }
//   );
//   return FetchAccountNameResponseSchema.parse(response.data);
// };

// export const useFetchAccountName = () => {
//   return useMutation({
//     mutationFn: FetchAccountName,
//   });
// };


const FetchAccountName = (fetchAccountNameDto: FetchAccountNameDto): Promise<AxiosResponse> => {

  
  return adminLoanAxios.post(`/send/fetch_account_name/`, fetchAccountNameDto, {});
};

export const useFetchAccountName = () => {
  return useMutation('fetchAccountNameDto', (fetchAccountNameDto: FetchAccountNameDto) => FetchAccountName(fetchAccountNameDto), {});
};
