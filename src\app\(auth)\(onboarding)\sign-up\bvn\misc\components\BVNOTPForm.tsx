'use client';

import type { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import PinInput from 'react-pin-input';

import { Button } from '@/components/core/Button';
import { ErrorModal } from '@/components/core/ErrorModal';
import { LoaderModal } from '@/components/core/LoaderModal';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';

import { useVerifyBVNOTP } from '../api';

interface BVNOTPFormProps {
  phone: string;
}

export function BVNOTPForm({ phone }: BVNOTPFormProps) {
  const [passcode, setPasscode] = React.useState('');

  const router = useRouter();

  const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
    useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { mutate: verifyBVNOTP, isLoading: isVerifyBVNOTPLoading } = useVerifyBVNOTP();

  const handleOTPValidation = (passcode: string) => {
    verifyBVNOTP(
      {
        otp: passcode,
        phone_number: phone,
        app_name: 'Liberty Agency BVN OTP',
      },
      {
        onSuccess: () => {
          openLoaderModal();
          router.push(`/sign-up/security-questions/?&phone=${phone}`);
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };

  const handleComplete = (passcode: string) => {
    setPasscode(passcode);
    handleOTPValidation(passcode);
  };

  const handleContinueClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault;
    handleOTPValidation(passcode);
  };

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen || isVerifyBVNOTPLoading} />

      <form className="relative">
        <PinInput
          autoSelect={false}
          initialValue="o"
          inputFocusStyle={{
            border: '3px solid #403C3A',
            borderRadius: '.625rem',
            padding: '0.5rem',
            outline: 'none',
            color: 'white',
            background: 'rgba(255, 255, 255, 0.3)',
            boxShadow: '0 0 0 1px rgb(255, 255, 255)',
          }}
          inputMode="number"
          inputStyle={{
            marginRight: '.3125rem',
            marginLeft: '.3125rem',
            background: '#F5F7F9',
            borderRadius: '14px',
            fontSize: '15px',
            fontWeight: '500',
            width: 'calc(16.5% - 10px)',
            height: 'unset',
            aspectRatio: '1',
            transitionDuration: '300ms',
            transitionProperty:
              'color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter',
            transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          length={6}
          style={{ maxWidth: '25rem' }}
          type="numeric"
          onComplete={handleComplete}
        />

        <Button
          className="my-11 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isVerifyBVNOTPLoading}
          type="button"
          variant="white"
          onClick={handleContinueClick}
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isVerifyBVNOTPLoading ? 'Loading' : 'Continue'}</span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
      </form>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
