'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import {
    Button,
    ErrorModal,
    FormError,
    Input,
    LoaderModal,
} from '@/components/core';
import { LendingIntrestsOptions } from '../modals/LendingIntrestsOptions';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useOnlendingPlan } from '../../api/onlendiningPlan';
import { IntrestDaysOptions } from '../modals/IntrestDaysOptions';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { usePostIntrestDaysOTP } from '../../api/postIntrestDays';

interface NewPlanDurationProps {
}

const WalletFormSchema = z.object({
    amount: z
        .string({ required_error: 'Please enter a valid amount' })
        .regex(/^\d+$/, { message: 'Please enter a valid amount' })
        .trim()
        .min(1, { message: 'Please enter a company size.' })
        .refine(value => parseFloat(value) >= 50000, {
            message: 'Amount must be greater than or equal to 50,000.',
        }),
    // paymentOption: z.enum(['ten_thirty_days', 'thirty_one_ninety_days', 'ninety_one_one_twenty_days',
    //     'one_twenty_one_days', 'one_eighty_one_days', 'two_forty_one_days', 'three_zero_one_days']),
    paymentOption: z.string(),
});

export type WalletFormValues = z.infer<typeof WalletFormSchema>;

export function NewPlanDuration({ }: NewPlanDurationProps) {

    const { emailAddress, setTarget, setIntrestDaysResponse } = useOnboardingPlan();


    const { mutate: postIntrestDays, isLoading: isPostIntrestDaysLoading } =
        usePostIntrestDaysOTP(emailAddress as string);

    const [intrestRanges, setIntrestRanges] = React.useState({
        maxDays: 0,
        minDays: 0
    })

    const { data: userPlan } = useOnlendingPlan(emailAddress as string)

    const {
        state: isIntrestDaysOptionsOpen,
        setState: setIntrestDaysOptionsState,
        setTrue: openIntrestDaysOptions,
    } = useBooleanStateControl();

    const {
        state: isLendingIntrestOptionsOpen,
        setState: setLendingIntrestOptionsState,
        setTrue: openLendingIntrestOptions,
    } = useBooleanStateControl();

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    const {
        handleSubmit,
        register,
        control,
        formState: { errors },
    } = useForm<WalletFormValues>({
        resolver: zodResolver(WalletFormSchema),
        defaultValues: {
            amount: undefined,
        },
    });

    const _amount = useWatch({
        control,
        name: 'amount',
    });

    const handleIntrestDaysChecker = () => {
        postIntrestDays(
            {
                max_days: intrestRanges.maxDays,
                min_days: intrestRanges.minDays
            },
            {
                onSuccess: (data) => {
                    // setCreatePlanResponse({
                    //     duration: data.data.data.duration,
                    //     id: data.data.data.id,
                    //     name: data.data.data.name,
                    //     quotation_id: data.data.data.quotation_id,
                    //     target: data.data.data.quotation_id,
                    // })
                    setIntrestDaysResponse(data.data)
                    openIntrestDaysOptions()
                },

                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage as string);
                },
            }
        );
    }

    const onWithdrawalSubmit = (submittedData: WalletFormValues) => {

        setTarget(submittedData.amount)

        openLendingIntrestOptions()
    };
    return (
        <>
            <LoaderModal
                isOpen={
                    isPostIntrestDaysLoading
                }
            />

            <div className="w-full relative">
                <p className="text-xl text-[#242424] font-semibold">
                    Set lending duration
                </p>
                <p className='text-xs text-[#4E4E4E]'>
                    Lend money to borrowers and earn with easy with few easy steps.
                </p>
                <form
                    className="mt-[30px] relative w-full"
                    onSubmit={handleSubmit(onWithdrawalSubmit)}
                >
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Amount</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            id="amount"
                            placeholder="Enter amount"
                            type="number"
                            {...register('amount')}
                        />

                        {errors?.amount && (
                            <FormError errorMessage={errors.amount.message} />
                        )}

                        <p className='text-xs text-[#4E4E4E] mt-[6px]'>Put your one off amount (min-₦50,000)</p>
                    </div>

                    <div className='mt-8'>
                        <p className='text-xs text-[#4E4E4E] font-semibold mb-[6px]'>Durations</p>
                        <p className='text-xs text-[#4E4E4E]'>Choose a preferred duration of loans you would like to give and <span className='font-semibold'>earn upfront</span> interest up to <span className='font-semibold'>20%</span></p>
                    </div>

                    {/* <div className='mt-3'>
                        <div className="radio-group space-y-2">

                            <div>
                                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor="ten_thirty_days">
                                    <div className="flex gap-[14px] items-center flex-row-reverse">
                                        <input
                                            id="ten_thirty_days"
                                            type="radio"
                                            value="ten_thirty_days"
                                            {...register('paymentOption', { required: true })}
                                            className=" focus:outline-none"
                                        />
                                        <p className='text-[#4E4E4E] text-xs font-medium'>3% p.a.</p>
                                    </div>
                                    <p className='text-[#4E4E4E] text-xs font-semibold'>10 days - 30 days</p>
                                </label>
                            </div>

                            <div>
                                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor="thirty_one_ninety_days">
                                    <div className="flex gap-[14px] items-center flex-row-reverse">
                                        <input
                                            id="thirty_one_ninety_days"
                                            type="radio"
                                            value="thirty_one_ninety_days"
                                            {...register('paymentOption', { required: true })}
                                            className=" focus:outline-none"
                                        />
                                        <p className='text-[#4E4E4E] text-xs font-medium'>3.5% p.a.</p>
                                    </div>
                                    <p className='text-[#4E4E4E] text-xs font-semibold'>31 days - 90 days</p>
                                </label>
                            </div>

                            <div>
                                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor="ninety_one_one_twenty_days">
                                    <div className="flex gap-[14px] items-center flex-row-reverse">
                                        <input
                                            id="ninety_one_one_twenty_days"
                                            type="radio"
                                            value="ninety_one_one_twenty_days"
                                            {...register('paymentOption', { required: true })}
                                            className=" focus:outline-none"
                                        />
                                        <p className='text-[#4E4E4E] text-xs font-medium'>6.5% p.a.</p>
                                    </div>
                                    <p className='text-[#4E4E4E] text-xs font-semibold'>91 days - 120 days</p>
                                </label>
                            </div>

                            <div>
                                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor="one_twenty_one_days">
                                    <div className="flex gap-[14px] items-center flex-row-reverse">
                                        <input
                                            id="one_twenty_one_days"
                                            type="radio"
                                            value="one_twenty_one_days"
                                            {...register('paymentOption', { required: true })}
                                            className=" focus:outline-none"
                                        />
                                        <p className='text-[#4E4E4E] text-xs font-medium'>8.5% p.a.</p>
                                    </div>
                                    <p className='text-[#4E4E4E] text-xs font-semibold'>121 days - 180 days</p>
                                </label>
                            </div>


                            <div>
                                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor="one_twenty_one_days">
                                    <div className="flex gap-[14px] items-center flex-row-reverse">
                                        <input
                                            id="one_twenty_one_days"
                                            type="radio"
                                            value="one_twenty_one_days"
                                            {...register('paymentOption', { required: true })}
                                            className=" focus:outline-none"
                                        />
                                        <p className='text-[#4E4E4E] text-xs font-medium'>10.5% p.a.</p>
                                    </div>
                                    <p className='text-[#4E4E4E] text-xs font-semibold'>181 days - 240days</p>
                                </label>
                            </div>


                            <div>
                                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor="two_forty_one_days">
                                    <div className="flex gap-[14px] items-center flex-row-reverse">
                                        <input
                                            id="two_forty_one_days"
                                            type="radio"
                                            value="two_forty_one_days"
                                            {...register('paymentOption', { required: true })}
                                            className=" focus:outline-none"
                                        />
                                        <p className='text-[#4E4E4E] text-xs font-medium'>10.5% p.a.</p>
                                    </div>
                                    <p className='text-[#4E4E4E] text-xs font-semibold'>241 days - 300days</p>
                                </label>
                            </div>

                            <div>
                                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor="three_zero_one_days">
                                    <div className="flex gap-[14px] items-center flex-row-reverse">
                                        <input
                                            id="three_zero_one_days"
                                            type="radio"
                                            value="three_zero_one_days"
                                            {...register('paymentOption', { required: true })}
                                            className=" focus:outline-none"
                                        />
                                        <p className='text-[#4E4E4E] text-xs font-medium'>10.5% p.a.</p>
                                    </div>
                                    <p className='text-[#4E4E4E] text-xs font-semibold'>301 days - 360days</p>
                                </label>
                            </div>

                            {errors.paymentOption && <span className="error">{errors.paymentOption.message}</span>}
                        </div>
                    </div> */}

                    <div className='mt-3'>
                        <div className="radio-group space-y-2">
                            {userPlan?.data.map((option, index) => {
                                return (
                                    <div key={index} onClick={() => {
                                        setIntrestRanges({
                                            maxDays: option.max_days,
                                            minDays: option.min_days
                                        })
                                        handleIntrestDaysChecker()
                                    }
                                    }
                                    >
                                        <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-[#F5FAFF]" htmlFor={`${index}`}>
                                            <div className="flex gap-[14px] items-center flex-row-reverse">
                                                <input
                                                    id={`${index}`}
                                                    type="radio"
                                                    value={`${index}`}
                                                    {...register('paymentOption', { required: true })}
                                                    className="focus:outline-none"
                                                />
                                                <p className='text-[#4E4E4E] text-xs font-medium'>{option.rate}</p>
                                            </div>
                                            <p className='text-[#4E4E4E] text-xs font-semibold'>{`${option.min_days} days loans - ${option.max_days} days loans`}</p>
                                        </label>
                                    </div>
                                )
                            })}
                            {errors?.paymentOption && (
                                <FormError errorMessage={errors?.paymentOption.message} />
                            )}

                        </div>
                    </div>

                    <div className='mt-10'>
                        <Button
                            className="font-sora  w-full rounded-lg bg-[#032282] px-4 py-[10px] text-right text-sm font-normal text-white"
                        // onClick={openSavedBenneficiariesModal}
                        >
                            Proceed
                        </Button>
                    </div>
                </form>
            </div>

            <IntrestDaysOptions
                heading='Choose your lending duration'
                isIntrestDaysOptionsOpen={isIntrestDaysOptionsOpen}
                setIntrestDaysOptionsState={setIntrestDaysOptionsState}
            />

            <LendingIntrestsOptions
                heading='Interest options'
                isLendingIntrestsOptionsOpen={isLendingIntrestOptionsOpen}
                setLendingIntrestsOptionsState={setLendingIntrestOptionsState}
            />

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </>
    );
}
