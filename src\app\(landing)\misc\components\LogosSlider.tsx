import React from 'react';
import { useEffect, useCallback } from 'react'
import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import AutoScroll from 'embla-carousel-auto-scroll';

const slides = [
    { src: "/images/landing/brands/liberty-assured.png", alt: "Liberty Assured" },
    { src: "/images/landing/brands/liberty-pay.png", alt: "Liberty Pay" },
    { src: "/images/landing/brands/liberty-tech.png", alt: "Liberty Tech" },
    { src: "/images/landing/brands/nem-health.png", alt: "Nem Health" },
    { src: "/images/landing/brands/vfd-bank.png", alt: "VFD Bank" },
]


const LogosSlider: React.FC = () => {
    const options: EmblaOptionsType = { loop: true }
    const [emblaRef, emblaApi] = useEmblaCarousel(options, [
        AutoScroll({ playOnInit: true, speed: 1.5 })
    ])


    const toggleAutoplay = useCallback(() => {
        const autoScroll = emblaApi?.plugins()?.autoScroll
        if (!autoScroll) return

        const playOrStop = autoScroll.isPlaying()
            ? autoScroll.stop
            : autoScroll.play
        playOrStop()
    }, [emblaApi])

    useEffect(() => {
        const autoScroll = emblaApi?.plugins()?.autoScroll
        if (!autoScroll) return

    }, [emblaApi])

    return (
        <section className='pb-16'>
            <p className="text-center md:text-lg text-[#ffffff71] py-4">
                Seeds is support and powered by
            </p>
            <div className="w-[92%] max-w-[85rem] m-auto"
                style={{
                    '--slide-height': '19rem',
                    '--slide-spacing': '1rem',
                    '--slide-size': '25%',
                } as React.CSSProperties}
            >
                <div className="overflow-hidden" ref={emblaRef}>
                    <div className="flex [touch-action:pan-y_pinch-zoom] [margin-left:calc(var(--slide-spacing)_*_-1)]"
                        onMouseEnter={toggleAutoplay}
                        onMouseLeave={toggleAutoplay}
                    >
                        {[...slides, ...slides].map((item, index) => (
                            <div className="[flex:0_0_var(--slide-size)] min-w-0 h-[30px] pl-[var(--slide-spacing)] [transform:translate3d(0,0,0)]" key={index}>
                                <img
                                    alt={`Slider image ${index + 1}`}
                                    className="w-full h-full object-contain"
                                    src={item.src}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    )
}

export default LogosSlider;