import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg
        fill="none"
        height={26}
        viewBox="0 0 35 26"
        width={35}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            clipRule="evenodd"
            d="M23.1038 9.93586C24.7397 11.5717 24.7942 14.1901 23.2674 15.8915L23.1038 16.0641L18.6411 20.266C18.218 20.6891 17.5321 20.6891 17.1091 20.266C16.7185 19.8755 16.6885 19.261 17.0189 18.836L17.1091 18.734L21.5718 14.5321C22.3734 13.7305 22.4156 12.457 21.6983 11.6057L21.5718 11.4679L17.1091 7.26602C16.686 6.84295 16.686 6.15703 17.1091 5.73396C17.4996 5.34343 18.1141 5.31339 18.5391 5.64384L18.6411 5.73396L23.1038 9.93586Z"
            fill="currentColor"
            fillRule="evenodd"
        />
        <g opacity={0.1}>
            <path
                clipRule="evenodd"
                d="M14.9788 9.93586C16.6147 11.5717 16.6692 14.1901 15.1424 15.8915L14.9788 16.0641L10.5161 20.266C10.093 20.6891 9.40712 20.6891 8.98405 20.266C8.59353 19.8755 8.56349 19.261 8.89393 18.836L8.98405 18.734L13.4468 14.5321C14.2484 13.7305 14.2906 12.457 13.5733 11.6057L13.4468 11.4679L8.98405 7.26602C8.56098 6.84295 8.56098 6.15703 8.98405 5.73396C9.37458 5.34343 9.98908 5.31339 10.4141 5.64384L10.5161 5.73396L14.9788 9.93586Z"
                fill="currentColor"
                fillRule="evenodd"
            />
        </g>
    </svg>
);
export default SVGComponent;
