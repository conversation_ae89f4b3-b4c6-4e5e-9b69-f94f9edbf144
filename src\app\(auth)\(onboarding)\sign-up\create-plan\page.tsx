import { OnboardingPageWrapper } from '../../misc/components';
import { CreatePlanForm } from './misc/components';

export default function GetStarted({
  searchParams,
}: {
  searchParams: { email: string, phone: string };
}) {
  const { email, phone } = searchParams;
  return (
    <>
      <div className='bg-[#000D36] relative p-6 block md:hidden'>
        <h1 className='font-heading font-semibold text-xl tracking-[2%] text-white'>Create plan</h1>
        <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-2'>
          In order to know you more and determine your
          lending capabilities please complete the details
          below to start lending
        </p>
      </div>

      <OnboardingPageWrapper
        heading="Create plan"
        subHeading=" In order to know you more and determine your
          lending capabilities please complete the details
          below to start lending"
      >
        <CreatePlanForm email={email} phone={phone} />
      </OnboardingPageWrapper>
    </>
  );
}
