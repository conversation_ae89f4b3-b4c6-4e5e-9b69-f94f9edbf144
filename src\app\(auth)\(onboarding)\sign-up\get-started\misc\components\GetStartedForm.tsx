'use client';

import {
  Button,
  ErrorModal,
  FormError,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  LinkButton
} from '@/components/core';
import { listOfStates, stateLGAMap } from '@/data';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';

import { LendingOnboardingOptions } from '@/app/(dashboard)/misc/components/modals/LendingOnboardingOptions';

import { zodResolver } from '@hookform/resolvers/zod';

import { Label } from '@radix-ui/react-label';

import type { AxiosError } from 'axios';

import { Controller, useForm, useWatch } from 'react-hook-form';

import { z } from 'zod';

import React from 'react';

import { useRouter } from 'next/navigation';

import { useRegisterUserDetails } from '../api';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { formatAxiosErrorMessage } from '@/utils';

const GetStartedFormSchema = z.object({
  phone_number: z
    .string({ required_error: 'Please enter your phone number.' })
    .trim()
    .min(1, { message: 'Please enter your phone number.' }),
  username: z
    .string({ required_error: 'Please enter a username.' })
    .trim()
    .min(1, { message: 'Please enter a username.' }),
  first_name: z
    .string({ required_error: 'Please enter your first name.' })
    .trim()
    .min(1, { message: 'Please enter your first name.' }),
  last_name: z
    .string({ required_error: 'Please enter your last name.' })
    .trim()
    .min(1, { message: 'Please enter your last name.' }),
  business_name: z.string().trim().optional(),
  email: z
    .string({ required_error: 'Please enter your email.' })
    .trim()
    .min(1, { message: 'Please enter your email.' }),
  gender: z
    .string({ required_error: 'Please select a gender.' })
    .trim()
    .min(1, { message: 'Please select a gender.' }),
  state: z
    .string({ required_error: 'Please select a state.' })
    .trim()
    .min(1, { message: 'Please select a state.' }),
  lga: z
    .string({ required_error: 'Please select a Local Government Area.' })
    .trim()
    .min(1, { message: 'Please select a Local Government Area.' }),
  nearest_landmark: z
    .string({
      required_error: 'Please enter the nearest landmark to your address.',
    })
    .trim()
    .min(1, { message: 'Please enter the nearest landmark to your address.' }),
  street: z
    .string({ required_error: 'Please enter your street.' })
    .trim()
    .min(1, { message: 'Please enter your street.' }),
  referral_code: z.string().nullable().optional(),
});

export type GetStartedFormValues = z.infer<typeof GetStartedFormSchema>;

interface GetStartedProps {
  referral_code: string | null
}

export function GetStartedForm({ referral_code }: GetStartedProps) {

  const router = useRouter()

  const [onBoardingOptionDetails, setOnboardingOptionDetails] = React.useState({
    phoneNumber: '',
    emailDetail: ''
  })

  const { setTokenOnboarding, setReferallCodeOnboading } = useOnboardingPlan();

  const {
    state: isLendingOptionOpen,
    setState: setLendingOptionState,
    setTrue: _openLendingOption,
  } = useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    control,
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = useForm<GetStartedFormValues>({
    resolver: zodResolver(GetStartedFormSchema),
  });

  const selectedStateValue = useWatch({
    control,
    name: 'state',
  }) as (typeof listOfStates)[number];

  const businessNameValue = useWatch({
    control,
    name: 'business_name',
  });

  const lgasInSelectedState = selectedStateValue
    ? stateLGAMap?.[selectedStateValue]
    : undefined;

  const {
    mutate: registerUserDetails,
    isLoading: isRegisterUserDetailsLoading,
  } = useRegisterUserDetails();


  React.useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-expect-error
    setValue('referal_code', referral_code)
  }, [referral_code, setValue])


  const onGetStartedSubmit = (submittedData: GetStartedFormValues) => {
    let referralCodeValue: string | null | undefined = submittedData.referral_code;

    // Check if referral_code is an empty string, set it to null
    if (submittedData.referral_code === "") {
      referralCodeValue = null;
    }

    const updateData = {
      ...submittedData,
      referral_code: referralCodeValue
    }

    registerUserDetails(
      { ...updateData, type_of_user: 'AGENT', source: 'ONLENDING' },
      {
        onSuccess: (data) => {
          // openLoaderModal();


          router.push(
            `/sign-up/email-otp?email=${submittedData.email}&phone=${submittedData.phone_number}`
          );

          setReferallCodeOnboading(null)

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          setTokenOnboarding(data?.data.access)

          setOnboardingOptionDetails({
            phoneNumber: submittedData.phone_number,
            emailDetail: submittedData.email
          })

          // openLendingOption()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );

  };

  return (
    <>


      <form
        className="relative z-10"
        onSubmit={handleSubmit(onGetStartedSubmit)}
      >
        <Label className="sr-only" htmlFor="phone">
          Phone Number
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="phone"
          placeholder="Phone Number"
          type="text"
          {...register('phone_number')}
        />
        {errors?.phone_number && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.phone_number.message}
          />
        )}

        <Label className="sr-only" htmlFor="username">
          Username
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="username"
          placeholder="Username"
          type="text"
          {...register('username')}
        />
        {errors?.username && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.username.message}
          />
        )}

        <Label className="sr-only" htmlFor="first-name">
          First name
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="first-name"
          placeholder="First Name"
          type="text"
          {...register('first_name')}
        />
        {errors?.first_name && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.first_name.message}
          />
        )}

        <Label className="sr-only" htmlFor="last-name">
          Last name
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="last-name"
          placeholder="Last Name"
          type="text"
          {...register('last_name')}
        />
        {errors?.last_name && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.last_name.message}
          />
        )}

        <Label className="sr-only" htmlFor="business-name">
          Business name
        </Label>
        <div className="relative mt-4">
          <Input
            className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
            id="business-name"
            placeholder="Business Name"
            type="text"
            {...register('business_name')}
          />

          {!businessNameValue && (
            <p className="absolute right-6 top-1/2 -translate-y-1/2 rounded text-xs text-white/70">
              (Optional)
            </p>
          )}
        </div>
        {errors?.business_name && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.business_name.message}
          />
        )}

        <Label className="sr-only" htmlFor="email">
          Email
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="email"
          placeholder="Email"
          type="email"
          {...register('email')}
        />
        {errors?.email && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.email.message}
          />
        )}

        <Label className="sr-only" htmlFor="state">
          Gender
        </Label>
        <Controller
          control={control}
          name="gender"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                iconClassName="fill-white"
                id="gender"
                ref={ref}
              >
                <SelectValue placeholder="Gender" />
              </SelectTrigger>
              <SelectContent>
                {['Male', 'Female'].map(gender => {
                  return (
                    <SelectItem key={gender} value={gender}>
                      {gender}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.gender && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.gender.message}
          />
        )}

        <Label className="sr-only" htmlFor="state">
          State
        </Label>
        <Controller
          control={control}
          name="state"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                iconClassName="fill-white"
                id="state"
                ref={ref}
              >
                <SelectValue placeholder="State" />
              </SelectTrigger>
              <SelectContent>
                {listOfStates.map(state => {
                  return (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.state && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.state.message}
          />
        )}

        <Label className="sr-only" htmlFor="lga">
          L.G.A
        </Label>
        <Controller
          control={control}
          name="lga"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                disabled={!lgasInSelectedState}
                iconClassName="fill-white"
                id="lga"
                ref={ref}
              >
                <SelectValue placeholder="L.G.A" />
              </SelectTrigger>
              <SelectContent>
                {lgasInSelectedState?.map(lga => {
                  return (
                    <SelectItem key={lga} value={lga}>
                      {lga}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.lga && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.lga.message}
          />
        )}

        <Label className="sr-only" htmlFor="nearest-landmark">
          Nearest Landmark
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="nearest-landmark"
          placeholder="Nearest Landmark"
          type="text"
          {...register('nearest_landmark')}
        />
        {errors?.nearest_landmark && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.nearest_landmark.message}
          />
        )}

        <Label className="sr-only" htmlFor="street">
          Street
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="street"
          placeholder="Street"
          type="text"
          {...register('street')}
        />
        {errors?.street && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.street.message}
          />
        )}

        <Label className="sr-only" htmlFor="referral_code">
          Referral code
        </Label>
        {referral_code === null ? <>
          <Input
            className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
            id="referral_code"
            placeholder="Referral code"
            type="text"
            {...register('referral_code')}
          />
          {errors?.referral_code && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.referral_code.message}
            />
          )}
        </> : <>
          <Input
            className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
            defaultValue={String(referral_code)}
            id="referral_code"
            placeholder="Referral code"
            type="text"
            {...register('referral_code')}
            readOnly
          />
          {errors?.referral_code && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.referral_code.message}
            />
          )}
        </>}

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isRegisterUserDetailsLoading}
          type="submit"
          variant="white"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isRegisterUserDetailsLoading ? 'Loading' : 'Next'}</span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>

        <LinkButton
          className="mt-3 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
          href="/login"
          type="button"
          variant="outlined"
        >
          <span className="text-xxs font-normal md:text-xs">
            Already have an account?
          </span>
          <span className="text-sm md:text-base">Login</span>
        </LinkButton>

      </form>


      <LendingOnboardingOptions
        heading={'Lending options'}
        isLendingOnboardingOptionsOpen={isLendingOptionOpen}
        setLendingOnboardingOptionsState={setLendingOptionState}
        onBoardingOptionDetails={onBoardingOptionDetails}
      />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
