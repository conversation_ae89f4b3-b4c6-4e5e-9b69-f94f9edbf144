import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';

import { cn } from '@/utils/classNames';
import { Button } from '@/components/core';
import { useRouter } from 'next/navigation';

export function LoanOptionsPageWrapper({
  children,
  heading,
  isCentered = false,
}: {
  children: React.ReactNode;
  heading: React.ReactNode;
  isCentered?: boolean;
}) {
  const router = useRouter()
  return (
    <>
      <main className="relative mx-auto max-w-[34.375rem] bg-white  md:h-auto md:min-h-0 md:w-full md:grow-0 md:overflow-y-auto ">
        <div className="relative h-screen  pb-5 ">
          <div
            className={cn(
              'absolute inset-0 rounded-[5px] bg-[#ffffff]'
            )}
          />
          <h1
            className={cn(
              'relative flex gap-2 items-center mb-1 font-clash text-xl font-semibold leading-[normal] px-4 py-7 text-white bg-[#032282]',
              isCentered && 'text-center'
            )}
          >
            <Button className='p-0 cursor-pointer' variant="unstyled" onClick={() => {
              router.back()
            }}>
              <svg fill="none" height="38" viewBox="0 0 38 38" width="38" xmlns="http://www.w3.org/2000/svg">
                <rect height="36" rx="18" stroke="#F4F4F6" width="36" x="1" y="1" />
                <mask height="36" id="mask0_17088_10963" maskUnits="userSpaceOnUse" width="36" x="1" y="1">
                  <rect fill="white" height="36" rx="18" width="36" x="1" y="1" />
                </mask>
                <g mask="url(#mask0_17088_10963)">
                  <path clipRule="evenodd" d="M16.1716 21.8284C14.6615 20.3184 14.6112 17.9014 16.0206 16.3309L16.1716 16.1716L20.291 12.2929C20.6815 11.9024 21.3147 11.9024 21.7052 12.2929C22.0657 12.6534 22.0934 13.2206 21.7884 13.6129L21.7052 13.7071L17.5858 17.5858C16.8458 18.3257 16.8069 19.5012 17.469 20.287L17.5858 20.4142L21.7052 24.2929C22.0958 24.6834 22.0958 25.3166 21.7052 25.7071C21.3447 26.0676 20.7775 26.0953 20.3852 25.7903L20.291 25.7071L16.1716 21.8284Z" fill="white" fillRule="evenodd" />
                </g>
              </svg>
            </Button>



            <Balancer className={cn(isCentered && 'text-center')}>
              {heading}
            </Balancer>
          </h1>

          <div className='bg-white relative'>
            {children}
          </div>


        </div>


      </main >
    </>
  );
}
