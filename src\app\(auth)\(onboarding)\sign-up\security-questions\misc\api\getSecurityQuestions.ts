
import { adminLoanAxios } from '@/lib/axios';

import { useQuery } from 'react-query';


export const getSecurityQUestions = async (token: string): Promise<{questions: string[]}> => {
  const headers = {
    'Authorization':`Bearer ${token}`
  }; 


  const { data } = await adminLoanAxios.get('/agency/get_security_questions/', {headers});
  return data;
};

export const useSecurityQuestions = (token: string) =>
  useQuery('security-questions', () => getSecurityQUestions(token), { cacheTime: 1000 * 60 * 5 });
