'use client';

// import type { AxiosError } from 'axios';

import React from 'react';

import PinInput from 'react-pin-input';
import {
  Button,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  LoaderModal,
  // LinkButton,
  // LoaderModal,
  SuccessModal,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { usePlanForPlanOTP } from '../../api/payForPlan';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
// import { formatAxiosErrorMessage } from '@/utils/errors';


export interface WithdrawEntity {
  disburse_pin: string;
  payout_choice: string;
  amount: number;
  account_number: string;
  account_name: string;
  bank_code: string;
  bank_name: string;
}

export interface bankDetails {
  bankName: string;
  bankCode: string;
  bankAccount: string;
  bankAccountName: string;
  withdrawAmount: number;
  narration: string;
}

interface EnterAgentsWithdrawalModalProps {
  isAgentPinModalOpen: boolean;
  setAgentPinModalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  bankAmount: string;
}

export function EnterAgentsWithdrawalModal({
  isAgentPinModalOpen,
  setAgentPinModalState,
  heading,
  // bankAmount
}: EnterAgentsWithdrawalModalProps) {
  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { emailAddress, createPlanResponse, wallet_type } = useOnboardingPlan();



  const { mutate: payForPlan, isLoading: isPayForPlanLoading } =
    usePlanForPlanOTP(emailAddress as string);


  const handlePinSubmit = (value: string) => {
    payForPlan(
      {
        plan_id: Number(createPlanResponse?.id),
        wallet_type: String(wallet_type),
        transaction_pin: Number(value)
      },
      {
        onSuccess: () => {
          openSuccessModal()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );

  };

  if (isPayForPlanLoading) {
    return (
      <div>
        <LoaderModal />
      </div>
    );
  }

  return (
    <Dialog open={isAgentPinModalOpen} onOpenChange={setAgentPinModalState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="py-10 text-center">
          <form>
            <div className="flex flex-col justify-center bg-[#F5FAFF] py-4 px-8 rounded-lg">


              <div className='mt-[14px] flex items-center gap-4 justify-center '>
                <h1 className='text-[#032282] text-xl font-extrabold'>Enter transaction pin</h1>
              </div>
            </div>
            <div className="pin-input-container mx-auto mt-[27px] flex w-full justify-between gap-4 text-xl">
              <PinInput
                autoSelect={false}
                initialValue="o"
                inputFocusStyle={{ borderColor: '#4C1961' }}
                inputMode="number"
                inputStyle={{
                  marginRight: '10px',
                  background: '#F5F7F9',
                  borderRadius: '14px',
                  border: '#ffffff',
                  fontSize: '14px',
                }}
                length={4}
                style={{ padding: '10px', margin: 'auto' }}
                type="numeric"
                onComplete={handlePinSubmit}
              />
            </div>
          </form>
        </DialogBody>
      </DialogContent>

      {/* <SuccessPayrollModal
        funText='"Enjoyyyyy!!!! 🙌🙌"'
        heading="Withdrawal Successful"
        isSuccessPayrollModalOpen={isSuccessModalOpen}
        setSuccessPayrollModalState={setSuccessModalState}
        subheading={`You have successfully withdrawn the sum of ₦${employeeBankDetails.withdrawAmount} from your wallet`}
      >
        <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
          <Button
            className="grow text-base"
            size="lg"
            variant="default"
            onClick={() => {
              location.reload();
            }}
          >
            Done
          </Button>
        </div>
      </SuccessPayrollModal> */}

      <SuccessModal
        heading="Successful"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState} />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 text-base"
            size="lg"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </Dialog>
  );
}
