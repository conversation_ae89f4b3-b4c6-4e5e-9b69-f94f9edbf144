import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface AllPlansData {
  status: boolean
  count: number
  next: string
  previous: string
  results: AllPlansEntity[]
}

export interface AllPlansEntity {
  id: number
  ajo_user: string
  user: User
  name: string
  onlending_type: string
  duration: number
  target: number
  interest_rate: number
  periodic_amount: number
  frequency: string
  hour: string
  maturity_date: string
  estimated_amount: number
  quotation_id: string
  amount_saved: number
  interest_type: string
  is_activated: boolean
  completed: boolean
  is_active: boolean
  interest_paid: boolean
}

export interface User {
  email: string
}

export const getAllPlans = async (email: string, filter: string): Promise<AllPlansData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get(`/onlending/plans?filter=${filter}`, {headers});
  return data;
};

export const useAllPlans = (email: string, filter: string) =>
  useQuery('all-lending-plans', () => getAllPlans(email, filter), );
 