import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface AccountCheckData {
  status: boolean
  data: AccountCheckEntity
}

export interface AccountCheckEntity {
  next_of_kin: boolean
}



export const getAccountCheck = async (email: string): Promise<AccountCheckData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
   
  const { data } = await savingsAxios.get(`/accounts/checks/`, {headers});
  return data;
};

export const useAccountCheck = (email: string) =>
  useQuery('accounts-check', () => getAccountCheck(email), );
 