'use client';

import React from 'react';

import {
  Dialog,
  DialogBody,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  LinkButton,
} from '@/components/core';

interface SetNextOfKinProps {
  isSetNextOfKinOpen: boolean;
  setNextOfKinState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  email: string;
  phone_number: string;
}


export function SetNextOfKin({
  isSetNextOfKinOpen,
  setNextOfKinState,
  heading,
  email,
  phone_number,
}: SetNextOfKinProps) {


  return (
    <Dialog open={isSetNextOfKinOpen} onOpenChange={setNextOfKinState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="text-left">
          <p className='text-xs font-semibold'>Complete next of kin to proceed</p>

          <LinkButton className='w-full mt-5 py-3' href={`sign-up/next-of-kin?email=${email}&phone=${phone_number}`} size={'fullWidth'}>
            Continue
          </LinkButton>

        </DialogBody>
      </DialogContent>

    </Dialog>
  );
}
