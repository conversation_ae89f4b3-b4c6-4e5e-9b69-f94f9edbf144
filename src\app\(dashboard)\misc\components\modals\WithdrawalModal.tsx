'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import {
  Button,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  FormError,
  Input,
  LoaderModal,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { addCommasToNumber } from '@/utils/numbers';
// import { EnterWithdrawalPinModal } from './EnterPaymentPinModal';
import { WidthrawalInfoData } from '../../api/withdrawals/getWithdrawalAcount';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useWithdrawFunds } from '../../api/withdrawals/withdrawFunds';
import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { SuccessModalWithdrawal } from './SuccessModalWithdrawal';
// import { SuccessModal } from './SuccessModal';

interface WithdrawalModalProps {
  isWithdrawalModalOpen: boolean;
  setWithdrawalModalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  children?: React.ReactNode;
  account_details: WidthrawalInfoData | undefined
}

const WalletFormSchema = z.object({
  amount: z
    .string({ required_error: 'Please enter a valid amount' })
    .regex(/^\d+$/, { message: 'Please enter a valid amount' })
    .trim()
    .min(1, { message: 'Please enter a company size.' }),
});

export type WalletFormValues = z.infer<typeof WalletFormSchema>;

export function WithdrawalModal({
  isWithdrawalModalOpen,
  setWithdrawalModalState,
  heading,
  account_details
}: WithdrawalModalProps) {
  const { emailAddress } = useOnboardingPlan();

  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();

  const {
    state: _isCreateWithdrawalPinModalOpen,
    setState: _setCreateWithdrawalPinModalState,
    setTrue: _openCreateWithdrawalPinModal,
  } = useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();


  const [_bankAmount, setBankAmount] = React.useState('');

  const {
    handleSubmit,
    register,
    control,
    formState: { errors },
  } = useForm<WalletFormValues>({
    resolver: zodResolver(WalletFormSchema),
    defaultValues: {
      amount: undefined,
    },
  });

  const amount = useWatch({
    control,
    name: 'amount',
  });

  const { mutate: postWithdrawFunds, isLoading: isPostWithdrawFundsLoading } =
    useWithdrawFunds(emailAddress as string);


  const onWithdrawalSubmit = (submittedData: WalletFormValues) => {
    setBankAmount(submittedData.amount)
    postWithdrawFunds({
      amount: submittedData.amount,
      wallet_type: "ONLENDING_MAIN"
    },
      {
        onSuccess: () => {
          openSuccessModal()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    )

    // openCreateWithdrawalPinModal()
  };


  if (isPostWithdrawFundsLoading) {
    return (
      <div>
        <LoaderModal />
      </div>
    );
  }

  return (
    <Dialog open={isWithdrawalModalOpen} onOpenChange={setWithdrawalModalState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="p-0 text-left">
          <div className="px-8 py-6">
            <p className="mt-1 text-[13px] text-[#6C727F]">
              Withdrawal will be made to the account details shown
              below
            </p>

            <div className='w-full bg-[#F5FAFF] mt-3 rounded-lg py-[22px] px-6'>
              <div>
                <p className='text-[#2F3A5B] text-xxs '>
                  Account name
                </p>
                <p className='text-[#032282] text-sm font-semibold'>{account_details?.data?.account_name}</p>
              </div>

              <div className='flex justify-between items-center mt-3'>
                <div>
                  <p className='text-[#2F3A5B] text-xxs '>
                    Bank name
                  </p>
                  <p className='text-[#032282] text-sm font-semibold'>{account_details?.data?.bank_name}</p>
                </div>
                <div>
                  <p className='text-[#2F3A5B] text-xxs '>
                    Account number
                  </p>
                  <p className='text-[#032282] text-sm font-semibold'>{account_details?.data?.account_number}</p>
                </div>
              </div>
            </div>
            <form
              className="mt-[30px] w-full space-y-8"
              onSubmit={handleSubmit(onWithdrawalSubmit)}
            >
              <Input
                autoCapitalize="none"
                autoComplete="off"
                autoCorrect="off"
                id="amount"
                placeholder="Enter amount"
                type="number"
                {...register('amount')}
              />

              {errors?.amount && (
                <FormError errorMessage={errors.amount.message} />
              )}

              <Button
                className="font-sora mt-8 w-full rounded-lg bg-[#032282] px-4 py-[10px] text-right text-sm font-normal text-white"
              // onClick={openSavedBenneficiariesModal}
              >
                <div className="flex w-full items-center justify-end gap-[18px] rounded-[5px]">
                  Withdraw
                  <span className="bg-[#062996] px-[27px] py-2 text-xs text-white">
                    ₦{addCommasToNumber(amount as unknown as number) || '00.00'}
                  </span>
                </div>
              </Button>
            </form>
          </div>
        </DialogBody>
      </DialogContent>


      {/* <EnterWithdrawalPinModal
        bankAmount={bankAmount}
        heading="Transaction Pin"
        isPinModalOpen={isCreateWithdrawalPinModalOpen}
        setPinModalState={setCreateWithdrawalPinModalState}
      /> */}

      <SuccessModalWithdrawal
        heading="Successful"
        isSuccessModalWithdrawalOpen={isSuccessModalOpen}
        setSuccessModalWithdrawalState={setSuccessModalState} />


      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>

    </Dialog>
  );
}
