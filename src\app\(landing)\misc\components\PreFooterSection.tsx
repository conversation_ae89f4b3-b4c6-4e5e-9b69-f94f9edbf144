

import { AppStoreIcon, PlayStoreIcon } from '../icons'

interface AppStoreButtonProps {
  store: "apple" | "google"
  className?: string
  href: string
}

export function AppStoreButton({ store, className, href }: AppStoreButtonProps) {
  const isApple = store === "apple"

  return (
    <a
      className={`inline-flex items-center gap-2 px-4 md:px-6 py-2 md:py-3 text-white rounded-xl bg-black hover:bg-black/90 transition-colors ${className}`}
      href={href}
      target='_blank'
    >
      {isApple ? (
        <AppStoreIcon className="w-4 md:w-6 h-4 md:h-6" />
      ) : (
        <PlayStoreIcon className="w-4 md:w-6 h-4 md:h-6" />
      )}
      <div className="flex flex-col items-start text-sm">
        <span className="text-xs">Download on</span>
        <span className="font-semibold">{isApple ? "App Store" : "Play Store"}</span>
      </div>
    </a>
  )
}



interface PhoneMockupProps {
  imageSrc: string
  className?: string
}

export function PhoneMockup({ imageSrc, className }: PhoneMockupProps) {
  return (
    <div className={`relative ${className}`}>
      {/* <div className="absolute inset-0 bg-blue-500/30 blur-3xl rounded-full" /> */}
      <img
        alt="App screenshot"
        className="relative w-full h-auto rounded-3xl"
        src={imageSrc}
      />
    </div>
  )
}



export default function PreFooter() {
  return (
    <section className="w-full  bg-white py-16 px-4 md:px-8 lg:px-16 h-[480px]">


      <article className="relative w-[95%] md:w-[85%] max-w-[1450px] mx-auto bg-[#1255C3] rounded-3xl overflow-hidden translate-y-[-30%] md:translate-y-[-40%]">
        <div className="grid lg:grid-cols-2 xl:grid-cols-[0.5fr,1fr] gap-8 p-8 pt-16 md:p-16 md:pt-32">

          <section className="">
            <h2 className="text-4xl lg:text-5xl font-semibold text-white leading-tight">
              Get financially librated
            </h2>
            <p className=" text-[#FFFFFFB2] mb-10 text-balance">
              Access easier means of financial stability and savings
            </p>
            <div className="space-y-4">
              <p className="text-white font-medium">Download the seeds app</p>
              <div className="flex flex-wrap gap-x-3 gap-y-1.5 md:gap-4">
                <AppStoreButton href="https://play.google.com/store/apps/details?id=com.libertytech.seedslite" store="google" />
                <AppStoreButton href="https://play.google.com/store/apps/details?id=com.libertytech.seedslite" store="apple" />
              </div>
            </div>
          </section>

          {/* Right Content - Phone Mockups */}
          <section className="relative">
            <div className="grid grid-cols-3">
              <PhoneMockup
                className="transform translate-y-8 translate-x-[20%] z-[2]"
                imageSrc="/images/landing/PHONE_MOCKUP_1.png"
              />
              <PhoneMockup
                className="transform  scale-[1.3] z-[3]"
                imageSrc="/images/landing/PHONE_MOCKUP_2.png"
              />
              <PhoneMockup
                className="transform translate-y-8 -translate-x-[20%] z-[2]"
                imageSrc="/images/landing/PHONE_MOCKUP_3.png"
              />
            </div>
            <div className='absolute bottom-0 w-full h-48 translate-y-[80%] md:translate-y-[65%] blur-lg [background:linear-gradient(90deg,#1255C3_118.53%,#000000_118.53%,#000619_118.53%)] z-[4]'></div>
            <div className="absolute flex items-center justify-center top-[-25%] w-full">
              <h6 className=" text-8xl lg:text-[12rem] text-center font-bold text-[#0443AC]">Seeds</h6>

            </div>
          </section>
        </div>
      </article>
    </section>
  )
}
