import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';

export interface AnswerSecurityQuestionsDTO {
  first_security_question: string;
  first_security_answer: string;
  second_security_question: string;
  second_security_answer: string;
}

const answerSecurityQuestions = (answerSecurityQuestionsDto:AnswerSecurityQuestionsDTO, token: string): Promise<AxiosResponse> => {

  const headers = {
    'Authorization':`Bearer ${token}`
  }; 
  
  return adminLoanAxios.post(`/agency/get_security_questions/`, answerSecurityQuestionsDto, {headers});
};

export const useAnswerSecurityQuestions = (token: string) => {
  return useMutation('answerSecurityQuestions', (answerSecurityQuestionsDto: AnswerSecurityQuestionsDTO) => answerSecurityQuestions(answerSecurityQuestionsDto, token), {});
};