'use client'

import { useSearchParams } from 'next/navigation';
import * as React from 'react';

import { WelcomePageWrapper, OnboardingPageWrapper } from '../misc/components';
import { LinkButton } from '@/components/core/LinkButton';
import { useOnboardingPlan } from '@/store/onBoardingPlan';

export default function Login() {
  const searchParams = useSearchParams();
  const referral_code = searchParams.get("referral_code");


  const { setReferallCodeOnboading } = useOnboardingPlan();

  React.useEffect(() => {
    if (referral_code === null) {

    } else {
      setReferallCodeOnboading(referral_code)
    }
  }, [referral_code, setReferallCodeOnboading])


  return (
    <>

      <div className='hidden sm:block'>
        <OnboardingPageWrapper
          heading=""
          subHeading=""
        >
          <div className='relative z-10'>
            <div className='mb-[18px]'>
              <svg fill="none" height="74" viewBox="0 0 74 74" width="74" xmlns="http://www.w3.org/2000/svg">
                <circle cx="37" cy="37" fill="white" r="37" />
                <path d="M45.3333 51.1666H28.6666C23.6666 51.1666 20.3333 48.6666 20.3333 42.8333V31.1666C20.3333 25.3333 23.6666 22.8333 28.6666 22.8333H45.3333C50.3333 22.8333 53.6666 25.3333 53.6666 31.1666V42.8333C53.6666 48.6666 50.3333 51.1666 45.3333 51.1666Z" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M37 42C39.7614 42 42 39.7614 42 37C42 34.2386 39.7614 32 37 32C34.2386 32 32 34.2386 32 37C32 39.7614 34.2386 42 37 42Z" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M26.1667 32.8333V41.1666" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M47.8333 32.8333V41.1666" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
              </svg>
            </div>

            <h1 className='font-heading font-semibold text-xl tracking-[2%] text-white'>Become a
              Seeds onlending partner  </h1>
            <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-2'>
              Seeds Onlending offers you the opportunity to lend money to borrowers with low risk while earning a competitive annual interest of 20%.
            </p>
            <p className='text-[#FFFFFFB2] text-sm leading-[19px] mt-6 font-normal font-sans'>
              As a Seeds Onlending partner, you can seamlessly service loans and enjoy effortless earning.
            </p>
            <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-6'>
              To profile and assess your lending capabilities, kindly click on button below to provide details and get started.</p>


            <div className='mt-6 z-[99999]'>
              <LinkButton className='flex py-3 text-base font-medium cursor-pointer' href="/sign-up/get-started" size="fullWidth" variant="white">
                <div>
                  Get started
                </div>

                <svg fill="none" height="20" viewBox="0 0 25 20" width="25" xmlns="http://www.w3.org/2000/svg">
                  <path clipRule="evenodd" d="M16.5221 7.64306C17.7805 8.90141 17.8224 10.9156 16.6479 12.2243L16.5221 12.3571L13.0892 15.5893C12.7638 15.9148 12.2362 15.9148 11.9107 15.5893C11.6103 15.2889 11.5872 14.8162 11.8414 14.4893L11.9107 14.4108L15.3436 11.1786C15.9602 10.562 15.9927 9.58241 15.441 8.92758L15.3436 8.82157L11.9107 5.58934C11.5853 5.2639 11.5853 4.73626 11.9107 4.41083C12.2111 4.11042 12.6838 4.08731 13.0107 4.3415L13.0892 4.41083L16.5221 7.64306Z" fill="#032180" fillRule="evenodd" />
                  <g opacity="0.3">
                    <path clipRule="evenodd" d="M11.5221 7.64306C12.7805 8.90141 12.8224 10.9156 11.6479 12.2243L11.5221 12.3571L8.08924 15.5893C7.7638 15.9148 7.23616 15.9148 6.91072 15.5893C6.61032 15.2889 6.58721 14.8162 6.8414 14.4893L6.91072 14.4108L10.3436 11.1786C10.9602 10.562 10.9927 9.58241 10.441 8.92758L10.3436 8.82157L6.91072 5.58934C6.58529 5.2639 6.58529 4.73626 6.91072 4.41083C7.21113 4.11042 7.68382 4.08731 8.01073 4.3415L8.08924 4.41083L11.5221 7.64306Z" fill="#032180" fillRule="evenodd" />
                  </g>
                </svg>

              </LinkButton>

              <LinkButton
                className="mt-3 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
                href="/login"
                type="button"
                variant="outlined"
              >
                <span className="text-xxs font-normal md:text-xs">
                  Already a lender?
                </span>
                <span className="text-sm md:text-base">Login</span>
              </LinkButton>
            </div>

            <div className='mt-7'>
              <h1 className='font-heading font-semibold text-xl tracking-[2%] text-white'>Become a referral agent </h1>
              <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-2'>
                To become a referral agent and earn a commission for every onlending partner referred, expedite your
                candidature by sending an email to <span className='font-bold'> <EMAIL></span>
              </p>
            </div>

          </div>
        </OnboardingPageWrapper>
      </div>

      <div className='block sm:hidden'>
        <WelcomePageWrapper
          heading=""
          subHeading=""
        >
          <div className='relative z-10 mt-6 pb-5'>
            <div className='mb-[18px]'>
              <svg fill="none" height="74" viewBox="0 0 74 74" width="74" xmlns="http://www.w3.org/2000/svg">
                <circle cx="37" cy="37" fill="white" r="37" />
                <path d="M45.3333 51.1666H28.6666C23.6666 51.1666 20.3333 48.6666 20.3333 42.8333V31.1666C20.3333 25.3333 23.6666 22.8333 28.6666 22.8333H45.3333C50.3333 22.8333 53.6666 25.3333 53.6666 31.1666V42.8333C53.6666 48.6666 50.3333 51.1666 45.3333 51.1666Z" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M37 42C39.7614 42 42 39.7614 42 37C42 34.2386 39.7614 32 37 32C34.2386 32 32 34.2386 32 37C32 39.7614 34.2386 42 37 42Z" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M26.1667 32.8333V41.1666" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M47.8333 32.8333V41.1666" stroke="#000D36" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
              </svg>
            </div>

            <h1 className='font-heading font-semibold text-xl tracking-[2%] text-white'>Become to
              Seeds onlending partner  </h1>
            <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-2'>
              Seeds Onlending offers you the opportunity to lend money to borrowers with low risk while earning a competitive annual interest of 20%.
            </p>
            <p className='text-[#FFFFFFB2] text-sm leading-[19px] mt-6 font-normal font-sans'>
              As a Seeds Onlending partner, you can seamlessly service loans and enjoy effortless earning.
            </p>
            <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-6'>
              To profile and assess your lending capabilities, kindly click on button below to provide details and get started.</p>


            <div className='mt-6 z-[99999]'>
              <LinkButton className='flex py-3 text-base font-medium cursor-pointer' href="/sign-up/get-started" size="fullWidth" variant="white">
                <div>
                  Get started
                </div>

                <svg fill="none" height="20" viewBox="0 0 25 20" width="25" xmlns="http://www.w3.org/2000/svg">
                  <path clipRule="evenodd" d="M16.5221 7.64306C17.7805 8.90141 17.8224 10.9156 16.6479 12.2243L16.5221 12.3571L13.0892 15.5893C12.7638 15.9148 12.2362 15.9148 11.9107 15.5893C11.6103 15.2889 11.5872 14.8162 11.8414 14.4893L11.9107 14.4108L15.3436 11.1786C15.9602 10.562 15.9927 9.58241 15.441 8.92758L15.3436 8.82157L11.9107 5.58934C11.5853 5.2639 11.5853 4.73626 11.9107 4.41083C12.2111 4.11042 12.6838 4.08731 13.0107 4.3415L13.0892 4.41083L16.5221 7.64306Z" fill="#032180" fillRule="evenodd" />
                  <g opacity="0.3">
                    <path clipRule="evenodd" d="M11.5221 7.64306C12.7805 8.90141 12.8224 10.9156 11.6479 12.2243L11.5221 12.3571L8.08924 15.5893C7.7638 15.9148 7.23616 15.9148 6.91072 15.5893C6.61032 15.2889 6.58721 14.8162 6.8414 14.4893L6.91072 14.4108L10.3436 11.1786C10.9602 10.562 10.9927 9.58241 10.441 8.92758L10.3436 8.82157L6.91072 5.58934C6.58529 5.2639 6.58529 4.73626 6.91072 4.41083C7.21113 4.11042 7.68382 4.08731 8.01073 4.3415L8.08924 4.41083L11.5221 7.64306Z" fill="#032180" fillRule="evenodd" />
                  </g>
                </svg>

              </LinkButton>

              <LinkButton
                className="mt-3 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
                href="/login"
                type="button"
                variant="outlined"
              >
                <span className="text-xxs font-normal md:text-xs">
                  Already a lender?
                </span>
                <span className="text-sm md:text-base">Login</span>
              </LinkButton>
            </div>

            <div className='mt-7'>
              <h1 className='font-heading font-semibold text-xl tracking-[2%] text-white'>Become a referral agent </h1>
              <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-2'>
                To become a referral agent and earn a commission for every onlending partner referred, expedite your
                candidature by sending an email to <span className='font-bold'> <EMAIL></span>
              </p>
            </div>

          </div>


        </WelcomePageWrapper>
      </div>
    </>
  );
}