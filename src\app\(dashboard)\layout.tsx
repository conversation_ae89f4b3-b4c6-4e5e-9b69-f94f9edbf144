import type { <PERSON><PERSON><PERSON> } from 'next';
import { DM_Sans, Manrope, Nuni<PERSON> } from 'next/font/google';
import { cn } from '@/utils/classNames';
import { Suspense } from 'react';
import { Toaster } from 'react-hot-toast';
import { Slideshow } from '../(auth)/(onboarding)/misc';
import Image from 'next/image';
import { Wrapper } from '../(auth)/(onboarding)/misc/components/Wrapper';
import ProtectedRouteGuard from '../(auth)/(onboarding)/misc/components/ProtectedRouteGuard';

// const dm_sans = DM_Sans({ subsets: ['latin'] });
const fontSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'DM_Sans',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontNunito = Nunito({
  subsets: ['latin'],
  variable: '--font-nunito',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Nunito',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontManrope = Manrope({
  subsets: ['latin'],
  variable: '--font-manrope',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Manrope',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});


export const metadata: Metadata = {
  title: 'Seeds Onlending',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <body
        className={cn(
          'font-sans',
          fontSans.variable,
          fontManrope.variable,
          fontNunito.variable,
        )}>

        <Toaster
          containerStyle={{
            zIndex: 99999,
          }}
          position="top-center"
          toastOptions={{
            style: {
              zIndex: 99999,
            },
          }}
        />

  
            <ProtectedRouteGuard>
              <Suspense fallback={
                <div className="flex h-screen w-screen bg-[url('/images/loading_screen.png')] items-center justify-center">
                  <div className="flex h-screen w-screen bg-[#0000008f] items-center justify-center">
                    <div className="flex size-40 animate-bounce items-center  justify-center rounded-full bg-purple-3 p-6">
                      <div className="animate-pulse">
                        <div className="w-[137px] h-[26.35px]">
                          <svg fill="none" height="25" viewBox="0 0 144 25" width="144" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11.696 24.34C4.794 24.34 0.918 21.62 0.918 16.316V16.112H6.018V16.724C6.018 18.832 7.072 19.682 11.696 19.682C15.776 19.682 16.728 19.07 16.728 17.54C16.728 16.146 15.946 15.636 13.6 15.228L7.208 14.31C3.128 13.664 0.714 11.624 0.714 7.918C0.714 4.484 3.502 0.88 11.152 0.88C18.156 0.88 21.42 4.11 21.42 8.904V9.108H16.286V8.632C16.286 6.456 15.164 5.538 10.642 5.538C6.97 5.538 5.848 6.252 5.848 7.714C5.848 9.04 6.596 9.516 8.5 9.856L14.892 10.876C19.992 11.692 21.828 14.174 21.828 17.302C21.828 21.008 18.938 24.34 11.696 24.34ZM33.6289 24.34C27.9509 24.34 24.0749 21.552 24.0749 15.5C24.0749 10.06 27.9169 6.626 33.5269 6.626C39.1029 6.626 42.8089 9.55 42.8089 14.888C42.8089 15.5 42.7409 15.942 42.6729 16.52H28.8009C28.9369 19.138 30.1609 20.26 33.4589 20.26C36.4849 20.26 37.5389 19.478 37.5389 18.016V17.676H42.6389V18.05C42.6389 21.756 39.0349 24.34 33.6289 24.34ZM33.4249 10.604C30.3989 10.604 29.1069 11.624 28.8689 13.834H37.9129C37.7769 11.59 36.4169 10.604 33.4249 10.604ZM54.6956 24.34C49.0176 24.34 45.1416 21.552 45.1416 15.5C45.1416 10.06 48.9836 6.626 54.5936 6.626C60.1696 6.626 63.8756 9.55 63.8756 14.888C63.8756 15.5 63.8076 15.942 63.7396 16.52H49.8676C50.0036 19.138 51.2276 20.26 54.5256 20.26C57.5516 20.26 58.6056 19.478 58.6056 18.016V17.676H63.7056V18.05C63.7056 21.756 60.1016 24.34 54.6956 24.34ZM54.4916 10.604C51.4656 10.604 50.1736 11.624 49.9356 13.834H58.9796C58.8436 11.59 57.4836 10.604 54.4916 10.604ZM74.3344 24.34C69.0304 24.34 66.1064 20.872 66.1064 15.5C66.1064 10.06 68.9964 6.626 74.0624 6.626C78.1084 6.626 80.1824 8.632 80.7604 11.488H81.0664V1.22H86.1664V24H81.4064V19.274H81.1344C80.4884 22.64 78.2444 24.34 74.3344 24.34ZM71.2744 15.5C71.2744 18.594 72.8044 19.682 76.0684 19.682C79.2984 19.682 81.0664 18.56 81.0664 15.602V15.33C81.0664 12.372 79.3324 11.284 76.0684 11.284C72.8044 11.284 71.2744 12.372 71.2744 15.5ZM98.1468 24.34C92.4008 24.34 89.2388 22.096 89.2388 18.152V18.05H94.3388V18.356C94.3388 19.886 95.2908 20.294 98.1808 20.294C100.901 20.294 101.547 19.852 101.547 18.832C101.547 17.88 101.037 17.608 99.0308 17.336L94.2368 16.758C90.8368 16.384 88.9328 14.854 88.9328 11.998C88.9328 9.006 91.4828 6.626 97.3308 6.626C102.907 6.626 106.069 8.734 106.069 12.882V12.984H100.969V12.78C100.969 11.386 100.289 10.672 97.1608 10.672C94.6108 10.672 93.9648 11.114 93.9648 12.202C93.9648 13.086 94.4408 13.46 96.6848 13.732L100.323 14.174C104.879 14.684 106.579 16.214 106.579 19.036C106.579 22.232 103.417 24.34 98.1468 24.34ZM115.539 24H108.977V18.05H115.539V24ZM124.818 24H118.256V18.05H124.818V24ZM134.098 24H127.536V18.05H134.098V24ZM143.377 24H136.815V18.05H143.377V24Z" fill="white" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              }>
                <Wrapper>
                  <div className="h-screen-small justify-between overflow-auto bg-[#000D36] md:flex md:flex-row-reverse md:justify-normal md:pb-0">
                    <div className="relative md:flex md:basis-1/2 md:flex-col md:justify-center md:overflow-y-auto md:py-8 md:[@media(min-height:520px)]:items-center">
                      <div
                        className="absolute inset-y-0 left-[-10%] right-0 md:fixed md:left-auto md:w-[50vw]"
                        aria-hidden
                      >
                        <Image
                          alt="login pattern"
                          blurDataURL="eDI4z}~TIW_49a]yyD=_Ip%N00nh%MogxtI[^iS$of9ajDxaNexCoz"
                          className="w-full object-cover"
                          placeholder="blur"
                          sizes="100vw"
                          src="/images/login/login-image.png"
                          fill
                        />

                        {/* <div
            className={cn(
              'absolute inset-0 -scale-x-100 from-black/80 from-[50.08%] to-[#032282]/80 to-[104.44%] bg-gradient-225'
            )}
          /> */}
                      </div>


                      {/* <header className="relative mx-auto flex w-full max-w-[32.375rem] items-center justify-between gap-4 px-6 py-[3vh] bg-[#000D36] h-[92px] md:hidden">
          <svg fill="none" height="42" viewBox="0 0 76 42" width="76" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.256 23.24C3.384 23.24 0.648 21.32 0.648 17.576V17.432H4.248V17.864C4.248 19.352 4.992 19.952 8.256 19.952C11.136 19.952 11.808 19.52 11.808 18.44C11.808 17.456 11.256 17.096 9.6 16.808L5.088 16.16C2.208 15.704 0.504 14.264 0.504 11.648C0.504 9.224 2.472 6.68 7.872 6.68C12.816 6.68 15.12 8.96 15.12 12.344V12.488H11.496V12.152C11.496 10.616 10.704 9.968 7.512 9.968C4.92 9.968 4.128 10.472 4.128 11.504C4.128 12.44 4.656 12.776 6 13.016L10.512 13.736C14.112 14.312 15.408 16.064 15.408 18.272C15.408 20.888 13.368 23.24 8.256 23.24ZM23.7381 23.24C19.7301 23.24 16.9941 21.272 16.9941 17C16.9941 13.16 19.7061 10.736 23.6661 10.736C27.6021 10.736 30.2181 12.8 30.2181 16.568C30.2181 17 30.1701 17.312 30.1221 17.72H20.3301C20.4261 19.568 21.2901 20.36 23.6181 20.36C25.7541 20.36 26.4981 19.808 26.4981 18.776V18.536H30.0981V18.8C30.0981 21.416 27.5541 23.24 23.7381 23.24ZM23.5941 13.544C21.4581 13.544 20.5461 14.264 20.3781 15.824H26.7621C26.6661 14.24 25.7061 13.544 23.5941 13.544ZM38.6087 23.24C34.6007 23.24 31.8647 21.272 31.8647 17C31.8647 13.16 34.5767 10.736 38.5367 10.736C42.4727 10.736 45.0887 12.8 45.0887 16.568C45.0887 17 45.0407 17.312 44.9927 17.72H35.2007C35.2967 19.568 36.1607 20.36 38.4887 20.36C40.6247 20.36 41.3687 19.808 41.3687 18.776V18.536H44.9687V18.8C44.9687 21.416 42.4247 23.24 38.6087 23.24ZM38.4647 13.544C36.3287 13.544 35.4167 14.264 35.2487 15.824H41.6327C41.5367 14.24 40.5767 13.544 38.4647 13.544ZM52.4713 23.24C48.7273 23.24 46.6633 20.792 46.6633 17C46.6633 13.16 48.7033 10.736 52.2793 10.736C55.1353 10.736 56.5993 12.152 57.0073 14.168H57.2233V6.92H60.8233V23H57.4633V19.664H57.2713C56.8153 22.04 55.2313 23.24 52.4713 23.24ZM50.3113 17C50.3113 19.184 51.3913 19.952 53.6953 19.952C55.9753 19.952 57.2233 19.16 57.2233 17.072V16.88C57.2233 14.792 55.9993 14.024 53.6953 14.024C51.3913 14.024 50.3113 14.792 50.3113 17ZM69.2801 23.24C65.2241 23.24 62.9921 21.656 62.9921 18.872V18.8H66.5921V19.016C66.5921 20.096 67.2641 20.384 69.3041 20.384C71.2241 20.384 71.6801 20.072 71.6801 19.352C71.6801 18.68 71.3201 18.488 69.9041 18.296L66.5201 17.888C64.1201 17.624 62.7761 16.544 62.7761 14.528C62.7761 12.416 64.5761 10.736 68.7041 10.736C72.6401 10.736 74.8721 12.224 74.8721 15.152V15.224H71.2721V15.08C71.2721 14.096 70.7921 13.592 68.5841 13.592C66.7841 13.592 66.3281 13.904 66.3281 14.672C66.3281 15.296 66.6641 15.56 68.2481 15.752L70.8161 16.064C74.0321 16.424 75.2321 17.504 75.2321 19.496C75.2321 21.752 73.0001 23.24 69.2801 23.24Z" fill="white" />
            <path d="M1.24 39H0.6V32.3H1.31V35.44H1.36C1.57 34.59 2.28 33.96 3.47 33.96C5.02 33.96 5.87 35.03 5.87 36.53C5.87 38.03 5.02 39.1 3.41 39.1C2.31 39.1 1.52 38.53 1.29 37.54H1.24V39ZM1.31 36.61C1.31 37.78 2.05 38.45 3.24 38.45C4.42 38.45 5.15 37.96 5.15 36.53C5.15 35.1 4.4 34.62 3.26 34.62C2.01 34.62 1.31 35.3 1.31 36.52V36.61ZM7.39258 40.7H6.77258V40.05H7.52258C8.02258 40.05 8.21258 39.9 8.40258 39.49L8.64258 38.99L6.19258 34.06H6.97258L8.42258 37L8.97258 38.21H9.03258L9.56258 36.99L10.9326 34.06H11.7126L9.03258 39.69C8.67258 40.45 8.21258 40.7 7.39258 40.7ZM19.1928 39H14.2328V32.3H14.9428V38.35H19.1928V39ZM20.7045 33.43H19.9945V32.3H20.7045V33.43ZM20.7045 39H19.9945V34.06H20.7045V39ZM22.5388 39H21.8988V32.3H22.6088V35.44H22.6588C22.8688 34.59 23.5788 33.96 24.7688 33.96C26.3188 33.96 27.1688 35.03 27.1688 36.53C27.1688 38.03 26.3188 39.1 24.7088 39.1C23.6088 39.1 22.8188 38.53 22.5888 37.54H22.5388V39ZM22.6088 36.61C22.6088 37.78 23.3488 38.45 24.5388 38.45C25.7188 38.45 26.4488 37.96 26.4488 36.53C26.4488 35.1 25.6988 34.62 24.5588 34.62C23.3088 34.62 22.6088 35.3 22.6088 36.52V36.61ZM30.3914 39.1C28.8014 39.1 27.7914 38.1 27.7914 36.53C27.7914 35.03 28.7914 33.96 30.3814 33.96C31.8314 33.96 32.8614 34.8 32.8614 36.27C32.8614 36.45 32.8414 36.6 32.8114 36.73H28.4614C28.5014 37.84 29.0714 38.51 30.3814 38.51C31.5414 38.51 32.0814 38.08 32.0814 37.36V37.29H32.7914V37.36C32.7914 38.39 31.7714 39.1 30.3914 39.1ZM30.3714 34.55C29.0914 34.55 28.5114 35.21 28.4614 36.29H32.1914C32.1914 36.24 32.1914 36.19 32.1914 36.14C32.1914 35.1 31.5314 34.55 30.3714 34.55ZM34.5131 39H33.8031V34.06H34.4431V35.41H34.4931C34.6431 34.62 35.2031 33.96 36.2331 33.96C37.3731 33.96 37.8731 34.8 37.8731 35.72V36.21H37.1631V35.83C37.1631 34.99 36.8131 34.58 35.9531 34.58C34.9531 34.58 34.5131 35.21 34.5131 36.32V39ZM41.7257 39H40.6557C39.6757 39 39.0457 38.59 39.0457 37.43V34.67H38.1657V34.06H39.0457V32.88H39.7657V34.06H41.7257V34.67H39.7657V37.47C39.7657 38.16 40.1057 38.35 40.8257 38.35H41.7257V39ZM43.1543 40.7H42.5343V40.05H43.2843C43.7843 40.05 43.9743 39.9 44.1643 39.49L44.4043 38.99L41.9543 34.06H42.7343L44.1843 37L44.7343 38.21H44.7943L45.3243 36.99L46.6943 34.06H47.4743L44.7943 39.69C44.4343 40.45 43.9743 40.7 43.1543 40.7ZM48.8686 39H48.1586V32.3H51.2386C52.6886 32.3 53.7086 33.13 53.7086 34.56C53.7086 36 52.6886 36.83 51.2386 36.83H48.8686V39ZM51.1786 32.95H48.8686V36.18H51.1786C52.3586 36.18 52.9886 35.7 52.9886 34.56C52.9886 33.44 52.3586 32.95 51.1786 32.95ZM55.847 39.1C54.877 39.1 54.217 38.64 54.217 37.84C54.217 37.03 54.887 36.68 55.807 36.58L58.157 36.32V35.94C58.157 34.98 57.737 34.6 56.657 34.6C55.597 34.6 55.037 34.98 55.037 35.85V35.89H54.327V35.85C54.327 34.81 55.187 33.96 56.707 33.96C58.207 33.96 58.847 34.82 58.847 35.91V39H58.207V37.67H58.157C57.867 38.58 56.997 39.1 55.847 39.1ZM54.927 37.79C54.927 38.29 55.257 38.55 56.007 38.55C57.207 38.55 58.157 38.02 58.157 36.83V36.79L56.027 37.03C55.287 37.1 54.927 37.28 54.927 37.79ZM60.6641 40.7H60.0441V40.05H60.7941C61.2941 40.05 61.4841 39.9 61.6741 39.49L61.9141 38.99L59.4641 34.06H60.2441L61.6941 37L62.2441 38.21H62.3041L62.8341 36.99L64.2041 34.06H64.9841L62.3041 39.69C61.9441 40.45 61.4841 40.7 60.6641 40.7Z" fill="white" fillOpacity="0.8" />
          </svg>
        </header> */}

                      {children}

                    </div>

                    <Slideshow />

                  </div>
                </Wrapper>
              </Suspense>
            </ProtectedRouteGuard>
   

      </body>
    </html>
  );
}
