import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill="none"
    height={20}
    viewBox="0 0 20 20"
    width={20}
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <rect fill="url(#pattern0_1055_8260)"  height={20} width={20}/>
    <defs>
      <pattern
        height={1}
        id="pattern0_1055_8260"
        patternContentUnits="objectBoundingBox"
        width={1}
      >
        <use  transform="scale(0.00195312)" xlinkHref="#image0_1055_8260" />
      </pattern>
      <image
        height={512}
        id="image0_1055_8260"
        width={512}
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
);
export default SVGComponent;
