'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
import type { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { Balancer } from 'react-wrap-balancer';
import { z } from 'zod';

import { Button } from '@/components/core/Button';
import { ErrorModal } from '@/components/core/ErrorModal';
import { FormError } from '@/components/core/FormError';
import { Input } from '@/components/core/Input';
import { LinkButton } from '@/components/core/LinkButton';
import { LoaderModal } from '@/components/core/LoaderModal';
import {
  useBooleanStateControl,
  useClipboard,
  useErrorModalState,
} from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { launchNotification } from '@/utils/notifications';

import { useVerifyBVN } from '../api';

const BVNFormSchema = z.object({
  bvn: z
    .string({ required_error: 'Please enter your BVN.' })
    .trim()
    .min(1, { message: 'Please enter your BVN.' }),
});

export type BVNFormValues = z.infer<typeof BVNFormSchema>;

interface BVNFormProps {
  phone: string;
  email: string;
}

export function BVNForm({ phone, email }: BVNFormProps) {
  const router = useRouter();

  const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
    useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<BVNFormValues>({
    resolver: zodResolver(BVNFormSchema),
  });

  const clipboard = useClipboard();

  const { mutate: verifyBVN, isLoading: isVerifyBVNLoading } = useVerifyBVN();

  const onBVNFormSubmit = (submittedData: BVNFormValues) => {
    verifyBVN(
      { bvn_number: submittedData.bvn, phone_number: phone, otp_type: 'TEXT' },
      {
        onSuccess: () => {
          openLoaderModal();
          router.push(`/sign-up/bvn/otp/?email=${email}&phone=${phone}`);
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen} />

      <form className="relative" onSubmit={handleSubmit(onBVNFormSubmit)}>
        <Label className="sr-only" htmlFor="bvn">
          BVN
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-6 h-auto rounded-lg !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="bvn"
          placeholder="BVN"
          type="text"
          {...register('bvn')}
        />
        {errors?.bvn && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.bvn.message}
          />
        )}

        <div className="mt-4 flex w-full gap-3 rounded-xl !bg-white/30 p-6 text-white">
          <svg
            fill="none"
            height={16}
            viewBox="0 0 16 16"
            width={16}
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx={8} cy={8} fill="#fff" r={7.5} stroke="#fff" />
            <path
              clipRule="evenodd"
              d="M5.443 6.324A2.324 2.324 0 0 1 7.766 4h.162a2.324 2.324 0 0 1 2.323 2.324v.64h.514a.93.93 0 0 1 .93.93v2.377a.93.93 0 0 1-.93.93H4.929A.93.93 0 0 1 4 10.27V7.894a.93.93 0 0 1 .93-.93h.513v-.64Zm2.356-1.477c-.77 0-1.394.624-1.394 1.394v.724H9.29V6.24c0-.77-.624-1.394-1.394-1.394h-.097Z"
              fill="#0934F6"
              fillRule="evenodd"
            />
          </svg>

          <div className="w-full">
            <h2 className="mb-1 text-sm">Why we need your BVN?</h2>

            <p className="mb-3.5 text-xxs">We only need access to your:</p>

            <ul className="block w-full max-w-[90%] list-inside space-y-1 border-b border-b-white/40 pb-2">
              <li className="list-image-[url(/images/lists/circular-dot.svg)] text-xs">
                <span className="relative left-1.5">Full Name</span>
              </li>
              <li className="list-image-[url(/images/lists/circular-dot.svg)] text-xs">
                <span className="relative left-1.5">Phone Number</span>
              </li>
              <li className="list-image-[url(/images/lists/circular-dot.svg)] text-xs">
                <span className="relative left-1.5">Date of Birth</span>
              </li>
            </ul>

            <p className="mt-2 max-w-[90%] text-xxs">
              <Balancer>
                Your BVN is safe and does not give access to your bank accounts
                or transactions as it would only be used for account creation.
              </Balancer>
            </p>

            <p className="mt-4 text-xs">Don’t know your BVN?</p>

            <div className="mt-4 flex flex-wrap items-center gap-4">
              <p className="text-sm">
                Dial: <span className="font-bold">*565*0#</span>
              </p>

              <Button
                className="items-center gap-1.5 rounded-lg px-2.5 py-1.5"
                type="button"
                variant="white"
                onClick={() => {
                  clipboard.copy('*565*0#');
                  launchNotification('neutral', 'Code copied');
                }}
              >
                <svg
                  fill="none"
                  height="12"
                  viewBox="0 0 12 12"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_12941_290)">
                    <path
                      d="M8.88168 9.62163C8.88168 10.0142 8.72573 10.3907 8.44812 10.6683C8.17052 10.9459 7.79401 11.1019 7.40142 11.1019H2.2205C1.82791 11.1019 1.4514 10.9459 1.17379 10.6683C0.89619 10.3907 0.740234 10.0142 0.740234 9.62163V4.44071C0.740234 4.04812 0.89619 3.67161 1.17379 3.39401C1.4514 3.11641 1.82791 2.96045 2.2205 2.96045V3.70058C2.0242 3.70058 1.83595 3.77856 1.69715 3.91736C1.55834 4.05616 1.48037 4.24442 1.48037 4.44071V9.62163C1.48037 9.81793 1.55834 10.0062 1.69715 10.145C1.83595 10.2838 2.0242 10.3618 2.2205 10.3618H7.40142C7.59771 10.3618 7.78597 10.2838 7.92477 10.145C8.06357 10.0062 8.14155 9.81793 8.14155 9.62163H8.88168Z"
                      fill="#0934F6"
                    />
                    <path
                      d="M4.44071 1.48024C4.24442 1.48024 4.05616 1.55822 3.91736 1.69702C3.77856 1.83582 3.70058 2.02408 3.70058 2.22038V7.4013C3.70058 7.59759 3.77856 7.78585 3.91736 7.92465C4.05616 8.06345 4.24442 8.14143 4.44071 8.14143H9.62163C9.81793 8.14143 10.0062 8.06345 10.145 7.92465C10.2838 7.78585 10.3618 7.59759 10.3618 7.4013V2.22038C10.3618 2.02408 10.2838 1.83582 10.145 1.69702C10.0062 1.55822 9.81793 1.48024 9.62163 1.48024H4.44071ZM4.44071 0.740112H9.62163C10.0142 0.740112 10.3907 0.896068 10.6683 1.17367C10.9459 1.45127 11.1019 1.82779 11.1019 2.22038V7.4013C11.1019 7.79389 10.9459 8.1704 10.6683 8.448C10.3907 8.7256 10.0142 8.88156 9.62163 8.88156H4.44071C4.04812 8.88156 3.67161 8.7256 3.39401 8.448C3.11641 8.1704 2.96045 7.79389 2.96045 7.4013V2.22038C2.96045 1.82779 3.11641 1.45127 3.39401 1.17367C3.67161 0.896068 4.04812 0.740112 4.44071 0.740112Z"
                      fill="#0934F6"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_12941_290">
                      <rect fill="white" height="11.8421" width="11.8421" />
                    </clipPath>
                  </defs>
                </svg>

                <span className="tetx-[#0934F6] text-[.5rem]">Copy</span>
              </Button>
            </div>
          </div>
        </div>

        <Button
          className="mt-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isVerifyBVNLoading}
          type="submit"
          variant="white"
        >
          {isVerifyBVNLoading ? 'Loading' : 'Verify'}

        </Button>

        <LinkButton
          className="mt-4 w-full gap-2 rounded-[.5625rem] border-white/30 py-[.9375rem] text-base leading-[normal] text-white"
          // href="/sign-up/welcome"
          href={`/sign-up/security-questions/?&phone=${phone}`}
          type="button"
          variant="outlined"
        >
          Skip
        </LinkButton>
      </form>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
