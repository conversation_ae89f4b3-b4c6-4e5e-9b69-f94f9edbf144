'use client';

import {
  Button,
  ErrorModal,
  FormError,
  Input,
  LoaderModal,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
import { useRouter } from 'next/navigation';

import { useForm } from 'react-hook-form';

import { z } from 'zod';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import React from 'react';
import { useOnlendingPlanNoToken } from '@/app/(dashboard)/misc/api/onlendiningPlanNoToken';

import { formatAxiosErrorMessage } from '@/utils';
import { AxiosError } from 'axios';
import { usePostIntrestDaysNoTokenOTP } from '@/app/(dashboard)/misc/api/postIntrestDaysNoToken';
import { IntrestDaysOptionsOnBoarding } from '@/app/(dashboard)/misc/components/modals/onBoarding/IntrestDaysOptionsOnBoarding';
import { LendingIntrestsOptionsOnboarding } from '@/app/(dashboard)/misc/components/modals/onBoarding/LendingIntrestsOptionsOnboarding';

const CreatePlanFormSchema = z.object({
  plan_name: z
    .string({ required_error: 'Please enter your phone number.' })
    .trim()
    .min(1, { message: 'Please enter your phone number.' }),
  amount: z
    .string({ required_error: 'Please enter a valid amount' })
    .regex(/^\d+$/, { message: 'Please enter a valid amount' })
    .trim()
    .min(1, { message: 'Please enter a company size.' })
    .refine(value => parseFloat(value) >= 50000, {
      message: 'Amount must be greater than or equal to 50,000.',
    }),
  // intrest_due: z
  //   .string({ required_error: 'Please select an intrest.' })
  //   .trim()
  //   .min(1, { message: 'Please select an intrest.' }),
  paymentOption: z.string(),
});

export type CreatePlanFormValues = z.infer<typeof CreatePlanFormSchema>;

interface CreatePlanFormProps {
  phone: string;
  email: string
}


export function CreatePlanForm({ phone, email }: CreatePlanFormProps) {


  const router = useRouter();


  const { emailAddress,
    setIntrestDaysResponse,
    setPlanNameOnboarding,
    setTargetOnboarding,
    interest_type_text_onboarding, } = useOnboardingPlan();

  const { mutate: postIntrestDays, isLoading: isPostIntrestDaysLoading } =
    usePostIntrestDaysNoTokenOTP(emailAddress as string);

  const [intrestRanges, setIntrestRanges] = React.useState({
    maxDays: 0,
    minDays: 0
  })

  const { data: userPlan } = useOnlendingPlanNoToken()
  const {
    state: isIntrestDaysOptionsOpen,
    setState: setIntrestDaysOptionsState,
    setTrue: openIntrestDaysOptions,
  } = useBooleanStateControl();

  const {
    state: isLendingIntrestOptionsOpen,
    setState: setLendingIntrestOptionsState,
    setTrue: openLendingIntrestOptions,
  } = useBooleanStateControl();

  const { state: isLoaderModalOpen, setTrue: _openLoaderModal } =
    useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {

    handleSubmit,
    register,
    formState: { errors },
  } = useForm<CreatePlanFormValues>({
    resolver: zodResolver(CreatePlanFormSchema),
  });


  const handleIntrestDaysChecker = () => {
    postIntrestDays(
      {
        max_days: intrestRanges.maxDays,
        min_days: intrestRanges.minDays
      },
      {
        onSuccess: (data) => {
          setIntrestDaysResponse(data.data)
          openIntrestDaysOptions()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );
  }


  const onGetStartedSubmit = (submittedData: CreatePlanFormValues) => {

    setTargetOnboarding(submittedData.amount)
    setPlanNameOnboarding(submittedData.plan_name)

    // router.push(`/sign-up/email-otp?email=${email}&phone=${phone}`);
    router.push(
      `/sign-up/create-passcode/?email=${email}&phone=${phone}`
    );
  };

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen || isPostIntrestDaysLoading} />

      <form
        className="relative z-10"
        onSubmit={handleSubmit(onGetStartedSubmit)}
      >

        <div>
          <Label className="sr-only" htmlFor="plan">
            plan name
          </Label>
          <p className='text-white text-xs mb-2'>Name your plan. E.g. New car, General savings.</p>
          <Input
            className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
            id="plan"
            placeholder="Enter plan name"
            type="text"
            {...register('plan_name')}
          />
          {errors?.plan_name && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.plan_name.message}
            />
          )}
        </div>

        <div className='mt-4'>
          <Label className="sr-only" htmlFor="amount">
            Amount
          </Label>
          <p className='text-white text-xs mb-2'>Enter amount for plan. E.g. New car, General savings.</p>
          <Input
            className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
            id="amount"
            placeholder="Enter amount"
            type="text"
            {...register('amount')}
          />
          {errors?.amount && (
            <FormError
              className="bg-red-900/40 text-white"
              errorMessage={errors.amount.message}
            />
          )}

        </div>

        <div className="mt-5">
          <div>
            <p className='text-xs text-white font-medium'>Duration</p>
            <p className='text-xxs text-white font-normal mt-[2px]'>Choose your preferred duration you want to lock your funds and
              earn upfront interest up to 20%</p>
            <div className='w-full  bg-[#BFBFBF] h-[0.5px] my-[6px]'></div>
          </div>
        </div>
        <div className="radio-group space-y-2">
          {/* 
           <div>
            <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-white" htmlFor="ten_thirty_days">
              <div className="flex gap-[14px] items-center flex-row-reverse">
                <input
                  id="ten_thirty_days"
                  type="radio"
                  value="ten_thirty_days"
                  {...register('paymentOption', { required: true })}
                  className=" focus:outline-none"
                />
                <p className='text-[#4E4E4E] text-xs font-medium'>3% p.a.</p>
              </div>
              <p className='text-[#4E4E4E] text-xs font-semibold'>10 days - 30 days</p>
            </label>
          </div> */}

          {userPlan?.data.map((option, index) => {
            return (
              <div key={index} onClick={() => {
                setIntrestRanges({
                  maxDays: option.max_days,
                  minDays: option.min_days
                })
                handleIntrestDaysChecker()
              }
              }
              >
                <label className="flex items-center flex-row-reverse justify-between gap-2 px-2 py-[15px] rounded-md cursor-pointer bg-white" htmlFor={`${index}`}>
                  <div className="flex gap-[14px] items-center flex-row-reverse">
                    <input
                      id={`${index}`}
                      type="radio"
                      value={`${index}`}
                      {...register('paymentOption', { required: true })}
                      className="focus:outline-none"
                    />
                    <p className='text-[#4E4E4E] text-xs font-medium'>{option.rate}</p>
                  </div>
                  <p className='text-[#4E4E4E] text-xs font-semibold'>{`${option.min_days} days loans - ${option.max_days} days loans`}</p>
                </label>
              </div>
            )
          })}
          {errors?.paymentOption && (
            <FormError errorMessage={errors?.paymentOption.message} />
          )}
        </div>


        <button className="login-autofill-text w-full !text-left login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium flex justify-between items-center text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70" type='button'
          onClick={openLendingIntrestOptions}>
          <p className='text-xs !text-left'>{interest_type_text_onboarding || "When do you want your interest paid to you?"}</p>

          <svg fill="none" height="7" viewBox="0 0 12 7" width="12" xmlns="http://www.w3.org/2000/svg">
            <path clipRule="evenodd" d="M8.35702 5.52222C7.09866 6.78058 5.08452 6.82253 3.77578 5.64806L3.64297 5.52222L0.41074 2.08936C0.0853024 1.76392 0.0853024 1.23628 0.41074 0.910846C0.711143 0.610443 1.18384 0.587335 1.51075 0.841522L1.58925 0.910846L4.82148 4.34371C5.4381 4.96033 6.41767 4.99278 7.07249 4.44107L7.17851 4.34371L10.4107 0.910846C10.7362 0.585409 11.2638 0.585409 11.5893 0.910846C11.8897 1.21125 11.9128 1.68394 11.6586 2.01085L11.5893 2.08936L8.35702 5.52222Z" fill="white" fillRule="evenodd" />
          </svg>

        </button>

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] mt-[52px] text-base leading-[normal]"
          type="submit"
          variant="white"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>Next</span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
      </form>

      <IntrestDaysOptionsOnBoarding
        heading='Choose your lending duration'
        isIntrestDaysOptionsOnBoardingOpen={isIntrestDaysOptionsOpen}
        setIntrestDaysOptionsOnBoardingState={setIntrestDaysOptionsState}
      />

      <LendingIntrestsOptionsOnboarding
        heading='Interest options'
        isLendingIntrestsOptionsOnboardingOpen={isLendingIntrestOptionsOpen}
        setLendingIntrestsOptionsOnboardingState={setLendingIntrestOptionsState}
      />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
