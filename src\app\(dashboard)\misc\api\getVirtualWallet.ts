import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';

export interface VirtualWalletData {
  status: boolean
  data: virtualWalletEntity
}

export interface virtualWalletEntity {
  account_number: string
  bank_name: string
  account_name: string
  form_type: string
}


export const getVirtualWallet = async (email: string): Promise<VirtualWalletData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
  const { data } = await savingsAxios.get('/onlending/bank-account/', {headers});
  return data;
};

export const useVirtualWallet = (email: string) =>
  useQuery('on-lending-virtualWallet', () => getVirtualWallet(email), );
 