import { <PERSON>Button } from "@/components/core";
import { OnboardingPageWrapper } from "../../misc/components";
// import SuccessIcon from "../misc/icons/SuccessIcon";


export default function ForgotPassword() {
    return (
        <OnboardingPageWrapper
            heading="Password Reset Successful"
            subHeading="You have successfully reset your password. Kindly use your new password to login."
        >
            <div className="relative flex justify-center items-center flex-col">

                <LinkButton
                    className="mt-6 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] "
                    href="/login"
                    type="button"
                    variant="white"
                >
                    Login
                </LinkButton>
            </div>
        </OnboardingPageWrapper>
    );
}