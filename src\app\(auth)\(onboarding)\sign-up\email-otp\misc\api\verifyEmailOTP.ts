import { adminLoanAxios } from '@/lib/axios';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface VerifyEmailDTO {
  email: string;
  passcode: string;
}

export interface VerifyEmailOTPResponse {
  message: string;
}

const verifyEmailOTP = (verifyEmailDTO: VerifyEmailDTO): Promise<AxiosResponse> => {
  return adminLoanAxios.post(`/agency/user/confirm_registration_email/`, verifyEmailDTO);
};

export const useVerifyEmailOTP = () => {
  return useMutation('verifyEmailOTP', verifyEmailOTP, {});
};
