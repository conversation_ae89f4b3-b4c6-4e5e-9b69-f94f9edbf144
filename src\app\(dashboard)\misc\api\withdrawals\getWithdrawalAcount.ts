import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { savingsAxios } from '@/lib/axios';

import { useQuery } from 'react-query';



export interface WidthrawalInfoData {
  status: boolean
  message: string
  data: WidthrawalInfoEntity
}

export interface WidthrawalInfoEntity {
  account_number: string
  account_name: string
  bank_name: string
}



export interface User {
  email: string
}

export const getWithdrawalAccount = async (email: string): Promise<WidthrawalInfoData> => {
    const headers = {
        'Email-Address': email, 
        'Authorization':`Bearer ${tokenStorage.getToken()}`
      }; 
      
   
  const { data } = await savingsAxios.get(`/payment/withdrawal-account/`, {headers});
  return data;
};

export const useWithdrawalAccount = (email: string) =>
  useQuery('withdrawal-ccounts', () => getWithdrawalAccount(email), );
 