'use client';

import { motion } from 'framer-motion';
import * as React from 'react';

export function ScrollingFeatures() {
  return (
    <motion.svg
      animate={{ rotate: 360 }}
      className="pointer-events-none z-10 mx-auto h-[11.5rem] w-[11.5rem] rounded-full xl:h-[21.1875rem] xl:w-[21.1875rem]"
      fill="none"
      height={487}
      initial={{ rotate: 0 }}
      transition={{ duration: 10, repeat: Infinity, ease: 'linear' }}
      viewBox="0 0 487 487"
      width={487}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M52.626 109.571c-4.52-3.172-10.777-2.089-13.76 2.558a243.177 243.177 0 0 0-36.5 99.95c-.715 5.476 3.37 10.336 8.872 10.824l101.189 8.973c5.502.488 10.315-3.587 11.253-9.03a121.565 121.565 0 0 1 14.889-40.769c2.79-4.767 1.736-10.985-2.785-14.157l-83.158-58.349Zm231.016-95.763c.95-5.441-2.688-10.643-8.163-11.37a243.177 243.177 0 0 0-105.987 9.424c-5.261 1.68-7.924 7.444-6.029 12.63l34.87 95.416c1.896 5.187 7.63 7.813 12.956 6.35a121.582 121.582 0 0 1 43.232-3.844c5.5.5 10.681-3.096 11.632-8.536l17.489-100.07Zm-129.2 14.195c-2.11-5.104-7.968-7.55-12.981-5.233a243.16 243.16 0 0 0-84.476 64.699c-3.544 4.236-2.708 10.529 1.67 13.896l80.531 61.924c4.378 3.366 10.627 2.514 14.342-1.573a121.59 121.59 0 0 1 34.458-26.391c4.914-2.521 7.365-8.332 5.256-13.436l-38.8-93.886Z"
        fill="#E4F5FF"
      />
      <path
        d="M467.523 178.817c5.306-1.532 8.385-7.084 6.636-12.322a243.176 243.176 0 0 0-54.96-91.113c-3.819-3.99-10.166-3.856-13.996.123l-70.449 73.191c-3.83 3.979-3.674 10.283-.023 14.427a121.594 121.594 0 0 1 22.418 37.166c1.963 5.162 7.467 8.24 12.774 6.708l97.6-28.18ZM400.241 70.866c3.712-4.089 3.42-10.43-.818-13.972A243.185 243.185 0 0 0 304.82 8.185c-5.344-1.393-10.676 2.053-11.848 7.45l-21.553 99.274c-1.172 5.397 2.27 10.682 7.552 12.293a121.61 121.61 0 0 1 38.589 19.869c4.38 3.364 10.681 3.096 14.393-.993l68.288-75.212Z"
        fill="#D9DCFC"
      />
      <path
        d="M463.644 320.355c5.214 1.821 7.985 7.533 5.952 12.668a243.176 243.176 0 0 1-59.869 87.965c-4.031 3.776-10.361 3.294-13.967-.889l-66.335-76.939c-3.606-4.183-3.106-10.47.767-14.408a121.59 121.59 0 0 0 24.42-35.881c2.243-5.047 7.908-7.82 13.122-5.999l95.91 33.483Z"
        fill="#F5E1FD"
      />
      <path
        d="M36.879 351.564c-4.894 2.559-10.956.675-13.313-4.32A243.177 243.177 0 0 1 .326 243.407c.002-5.523 4.683-9.812 10.2-9.583l101.5 4.215c5.518.23 9.763 4.894 9.988 10.413a121.574 121.574 0 0 0 9.48 42.355c2.148 5.088.297 11.117-4.596 13.676l-90.02 47.081Zm91.912 94.943c-2.717 4.808-8.83 6.521-13.523 3.609a243.185 243.185 0 0 1-75.943-74.531c-3-4.637-1.402-10.782 3.355-13.588l87.491-51.626c4.757-2.806 10.855-1.198 14.043 3.312a121.582 121.582 0 0 0 30.977 30.402c4.569 3.103 6.292 9.17 3.575 13.978l-49.975 88.444Zm130.403 29.638c.372 5.51-3.795 10.3-9.316 10.445a243.164 243.164 0 0 1-104.402-20.548c-5.055-2.227-7.095-8.238-4.663-13.197l44.738-91.205c2.432-4.959 8.411-6.965 13.553-4.948a121.62 121.62 0 0 0 42.586 8.382c5.522.082 10.295 4.204 10.667 9.715l6.837 101.356Z"
        fill="#D9DCFC"
      />
      <path
        d="M384.327 431.342c3.336 4.402 2.482 10.693-2.053 13.845a243.186 243.186 0 0 1-98.546 40.136c-5.447.913-10.452-2.991-11.141-8.471l-12.673-100.794c-.689-5.479 3.208-10.439 8.613-11.575a121.59 121.59 0 0 0 40.197-16.372c4.661-2.962 10.913-2.137 14.249 2.265l61.354 80.966Zm86.003-239.864c5.372-1.28 10.786 2.036 11.844 7.457a243.186 243.186 0 0 1-2.98 106.413c-1.359 5.353-6.951 8.36-12.243 6.782L369.6 283.101c-5.293-1.578-8.261-7.143-7.122-12.547a121.595 121.595 0 0 0 1.215-43.411c-.834-5.46 2.441-10.85 7.814-12.13l98.823-23.535Z"
        fill="#F5E1FD"
      />
      <path
        d="M403.8 170.22c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CCD0F8"
      />
      <path
        d="M407.35 139.278a3.266 3.266 0 0 1 2.307.955 3.27 3.27 0 0 1 .955 2.307 3.265 3.265 0 0 1-3.14 3.262 1.097 1.097 0 0 0-.243 0m1.92 8.388c.671-.14 1.305-.41 1.827-.811 1.454-1.09 1.454-2.889 0-3.979-.513-.392-1.137-.653-1.799-.802m-8.584-2.917a1.575 1.575 0 0 0-.307 0 4.12 4.12 0 0 1-3.98-4.129 4.14 4.14 0 0 1 2.554-3.824 4.136 4.136 0 0 1 4.509.899 4.131 4.131 0 0 1-2.776 7.054Zm-4.659 3.439c-2.256 1.51-2.256 3.97 0 5.471 2.563 1.714 6.766 1.714 9.329 0 2.255-1.51 2.255-3.971 0-5.471-2.554-1.706-6.757-1.706-9.329 0Z"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
      />
      <path
        d="M230.905 83.2c13.28 0 24.045-10.766 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CEECFE"
      />
      <path
        d="M239.665 54.122v2.255c0 1.473-.932 2.405-2.404 2.405h-3.188v-6.515c0-1.034.848-1.873 1.883-1.873a3.745 3.745 0 0 1 2.619 1.09 3.753 3.753 0 0 1 1.09 2.638Z"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M221.026 55.054v13.048a.927.927 0 0 0 1.491.745l1.593-1.193a.942.942 0 0 1 1.231.093l1.547 1.557a.94.94 0 0 0 1.323 0l1.566-1.566a.924.924 0 0 1 1.211-.084l1.594 1.193c.615.457 1.491.02 1.491-.745V52.258a1.869 1.869 0 0 1 1.864-1.864h-11.184c-2.795 0-3.727 1.668-3.727 3.728v.932Z"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M227.549 60.655h2.796m-2.796-3.728h2.796"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M224.749 60.646h.009m-.009-3.728h.009"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
      />
      <path
        d="M432.425 272.125c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#F1D3FD"
      />
      <path
        d="M443.94 248.889a.376.376 0 0 0-.336-.204h-.002c-.408-.091-3.095-1.233-3.375-2.657-.14-.717.389-1.447 1.573-2.172a.39.39 0 0 0 .133-.138.39.39 0 0 0 .048-.186.388.388 0 0 0-.05-.185.37.37 0 0 0-.135-.137c-1.018-.609-1.966-.882-2.816-.814a3.33 3.33 0 0 0-2.258 1.209c-.239.12-.493.209-.754.266a3.598 3.598 0 0 0-5.759-2.746 3.598 3.598 0 0 0-1.361 2.166 7.372 7.372 0 0 0-3.153.597 7.238 7.238 0 0 0-3.039 2.717 1.385 1.385 0 0 1-1.252-.665 1.859 1.859 0 0 1 .009-1.852 1.435 1.435 0 0 1 1.425-.675.38.38 0 0 0 .383-.351.38.38 0 0 0-.33-.402 2.204 2.204 0 0 0-2.123 1.035 2.578 2.578 0 0 0-.009 2.637 2.134 2.134 0 0 0 1.672 1.015c-.23 1.778-.181 3.131.151 4.233.331 1.101.907 1.851 1.519 2.649a11.883 11.883 0 0 1 2.218 4.093.372.372 0 0 0 .137.191.375.375 0 0 0 .223.074h2.405a.376.376 0 0 0 .361-.267c.191-.767.291-1.554.298-2.344h5.011l-.026 2.233a.371.371 0 0 0 .105.266.37.37 0 0 0 .261.116c.018 0 .306.009 1.065.009.389 0 .9-.002 1.562-.009a.37.37 0 0 0 .284-.133.376.376 0 0 0 .085-.302c-.311-2.003 1.987-3.635 4.895-5.7.276-.195.556-.395.842-.599a.388.388 0 0 0 .115-.133 3.09 3.09 0 0 0-.002-2.835Zm-11.564-7.739a2.842 2.842 0 1 1-.002 5.683 2.842 2.842 0 0 1 .002-5.683Zm10.933 10.148-.762.543c-1.546 1.097-2.881 2.046-3.836 3.042-.972 1.014-1.427 1.963-1.406 2.957-.606.006-1.213.006-1.819.002l.027-2.24a.386.386 0 0 0-.027-.146.386.386 0 0 0-.351-.236h-5.762a.377.377 0 0 0-.377.372 12.276 12.276 0 0 1-.211 2.239h-1.831a12.54 12.54 0 0 0-2.251-4.062c-1.19-1.551-2.051-2.675-1.475-6.658 1.725-2.652 3.828-3.078 5.554-3.067a3.582 3.582 0 0 0 1.393 2.789h-1.115a.38.38 0 0 0-.359.378.38.38 0 0 0 .359.377h6.63a.378.378 0 1 0 0-.755h-1.114a3.594 3.594 0 0 0 1.334-2.183 3.979 3.979 0 0 0 1.228-.41.358.358 0 0 0 .109-.088c.947-1.108 2.137-1.301 3.627-.584-1.663 1.224-1.584 2.389-1.189 3.177.442.885 1.429 1.571 2.179 1.99.353.198.719.373 1.094.524.165.066.29.11.389.139a2.334 2.334 0 0 1-.038 1.9Zm-5.149-4.651a1.022 1.022 0 0 0-.936.626 1.013 1.013 0 1 0 .936-.626Zm0 1.272a.264.264 0 0 1-.144-.043.269.269 0 0 1-.095-.116.262.262 0 0 1 .152-.342.26.26 0 0 1 .302.099.265.265 0 0 1-.032.327.26.26 0 0 1-.183.075Zm-8.371-7.794v-1.584a.378.378 0 0 1 .755 0v1.584a.38.38 0 0 1-.377.359.38.38 0 0 1-.378-.359Zm2.209-.602v-1.585a.376.376 0 0 1 .378-.358.377.377 0 0 1 .377.358v1.585a.38.38 0 0 1-.377.359.378.378 0 0 1-.378-.359Zm2.209.602v-1.584a.378.378 0 0 1 .756 0v1.584a.376.376 0 0 1-.378.378.378.378 0 0 1-.378-.378Z"
        fill="#4C1961"
      />
      <path
        d="M64.88 308.765c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CCD0F8"
      />
      <path
        d="M73.646 285.597v-3.507c0-4.383-1.753-6.136-6.136-6.136h-5.26c-4.383 0-6.136 1.753-6.136 6.136v5.26c0 4.383 1.753 6.136 6.136 6.136h3.507"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M73.646 285.597v-3.507c0-4.383-1.753-6.136-6.136-6.136h-5.26c-4.383 0-6.136 1.753-6.136 6.136v5.26c0 4.383 1.753 6.136 6.136 6.136h3.507"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M73.646 285.597v-3.507c0-4.383-1.753-6.136-6.136-6.136h-5.26c-4.383 0-6.136 1.753-6.136 6.136v5.26c0 4.383 1.753 6.136 6.136 6.136h3.507m2.63-2.253 1.385 1.377 2.998-3.507m-12.273-5.698v2.63m4.383-2.63v2.63m4.383-2.63v2.63"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M399.22 371.74c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#F1D3FD"
      />
      <path
        d="M404.718 345.924v4.66m-6.058 0a2.331 2.331 0 0 0 1.648-3.978 2.33 2.33 0 1 0-1.648 3.978Zm-2.795 3.262c0 .699-.196 1.361-.541 1.92a3.703 3.703 0 0 1-3.187 1.808 3.706 3.706 0 0 1-3.188-1.808 3.646 3.646 0 0 1-.54-1.92 3.728 3.728 0 0 1 7.456 0Z"
        stroke="#073D9F"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="m390.684 353.845.923.923 1.985-1.836"
        stroke="#073D9F"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M389.341 351.33v-5.872c0-3.262 1.864-4.66 4.66-4.66h9.319c2.796 0 4.66 1.398 4.66 4.66v5.592c0 3.262-1.864 4.66-4.66 4.66h-7.922"
        stroke="#073D9F"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M312.2 437.005c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#F1D3FD"
      />
      <path
        d="M316.584 420.412h-8.767c-2.63 0-4.383-1.315-4.383-4.384v-6.136c0-3.068 1.753-4.383 4.383-4.383h8.767c2.629 0 4.383 1.315 4.383 4.383v6.136c0 3.069-1.754 4.384-4.383 4.384Z"
        stroke="#4C1961"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M306.502 410.769v4.383m11.397-4.383v4.383m-5.699.438a2.632 2.632 0 0 0 2.43-3.636 2.643 2.643 0 0 0-.57-.854 2.643 2.643 0 0 0-1.86-.77 2.63 2.63 0 1 0 0 5.26Z"
        stroke="#4C1961"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M207.045 450.09c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CCD0F8"
      />
      <path
        d="M198.564 432.196v-10.252c0-3.728.932-4.66 4.659-4.66h6.524c3.728 0 4.66.932 4.66 4.66v9.32c0 .13 0 .261-.009.391"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M202.757 421.944h7.456m-7.456 3.262h4.66m-6.197 4.194h13.187v3.262a3.27 3.27 0 0 1-.956 2.306 3.27 3.27 0 0 1-2.306.956h-9.32a3.262 3.262 0 0 1-3.261-3.262v-.606a2.66 2.66 0 0 1 2.656-2.656Z"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M70.605 204.57c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CEECFE"
      />
      <path
        d="M61.84 181.06h14.9"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M76.741 179.017v6.268c-.026 2.499-.71 3.13-3.313 3.13h-8.276c-2.647 0-3.313-.657-3.313-3.27v-6.128c0-2.367.552-3.129 2.63-3.252.21-.009.438-.017.683-.017h8.276c2.647 0 3.313.657 3.313 3.269Z"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M79.371 175.905v6.128c0 2.367-.552 3.129-2.63 3.252v-6.268c0-2.612-.666-3.27-3.313-3.27h-8.276c-.245 0-.473.009-.684.018.027-2.499.711-3.13 3.314-3.13h8.276c2.647 0 3.313.658 3.313 3.27Z"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M64.688 185.618h1.507m1.876 0h3.016"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M137.015 124.42c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CEECFE"
      />
      <path
        d="M144.466 99.762v-3.735c0-3.533-.824-4.418-4.138-4.418h-6.627c-3.314 0-4.138.885-4.138 4.418v9.871c0 2.332 1.28 2.884 2.832 1.219l.009-.009c.718-.763 1.814-.701 2.437.131l.885 1.184"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M133.508 95.992h7.013m-6.136 3.507h5.26"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
      />
      <path
        d="m142.46 102.804-3.104 3.103a1.105 1.105 0 0 0-.263.517l-.166 1.184c-.062.429.236.727.666.666l1.183-.167c.167-.026.404-.14.518-.263l3.103-3.103c.535-.535.789-1.157 0-1.946-.78-.78-1.403-.526-1.937.009Z"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M142.011 103.25a2.795 2.795 0 0 0 1.946 1.947"
        stroke="#5879FD"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
      />
      <path
        d="M324.795 109.535c13.28 0 24.045-10.766 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045-13.28 0-24.045 10.765-24.045 24.045 0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CCD0F8"
      />
      <path
        d="M316.905 94.257h15.78m-13.5-11.94h-1.403a.88.88 0 0 0-.877.877v7.556a.878.878 0 0 0 .877.877h1.403a.88.88 0 0 0 .876-.877v-7.556a.877.877 0 0 0-.876-.877Zm6.311-2.796h-1.402a.88.88 0 0 0-.877.876V90.75a.878.878 0 0 0 .877.877h1.402a.88.88 0 0 0 .877-.877V80.397a.88.88 0 0 0-.877-.876Zm6.312-2.797h-1.402a.88.88 0 0 0-.877.877v13.15c0 .482.394.876.877.876h1.402a.88.88 0 0 0 .877-.877V77.601a.878.878 0 0 0-.877-.877Z"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M114.045 399.09c13.28 0 24.045-10.765 24.045-24.045 0-13.28-10.765-24.045-24.045-24.045C100.765 351 90 361.765 90 375.045c0 13.28 10.765 24.045 24.045 24.045Z"
        fill="#CCD0F8"
      />
      <path
        d="M110.623 374.168h.008m2.448 0h4.822m-7.278-3.506h.008m2.448 0h4.822m-8.476 11.133c.719-.771 1.815-.71 2.446.132l.885 1.183c.71.938 1.859.938 2.569 0l.885-1.183c.631-.842 1.727-.903 2.446-.132 1.561 1.666 2.832 1.114 2.832-1.218v-9.88c.008-3.533-.816-4.418-4.129-4.418h-6.628c-3.313 0-4.137.885-4.137 4.418v9.871c0 2.341 1.279 2.884 2.831 1.227Z"
        stroke="#3838FF"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <motion.path
        animate={{ rotate: -360 }} // Opposite direction rotation
        d="M243.927 355.854c62.92 0 113.927-51.007 113.927-113.927 0-62.92-51.007-113.927-113.927-113.927C181.007 128 130 179.007 130 241.927c0 62.92 51.007 113.927 113.927 113.927Z"
        fill="#032282"
        initial={{ rotate: 0 }}
        transition={{ duration: 10, repeat: Infinity, ease: 'linear' }}
      />
      <motion.path
        animate={{ rotate: -360 }} // Opposite direction rotation
        d="M187.976 241h-6.016v-21.44h11.872c5.088 0 8.288 2.912 8.288 8s-3.2 8.032-8.288 8.032h-5.856V241Zm5.184-16.032h-5.184v5.216h5.184c2.112 0 2.976-.416 2.976-2.624 0-2.176-.864-2.592-2.976-2.592Zm15.454 16.352c-3.648 0-5.696-1.664-5.696-4.416 0-2.272 1.568-3.904 5.216-4.256l6.56-.64v-.32c0-1.632-.704-1.888-2.849-1.888-1.983 0-2.591.384-2.591 1.728v.128h-6.016v-.064c0-4.288 3.584-7.04 9.056-7.04 5.632 0 8.352 2.752 8.352 7.264V241h-5.632v-3.392h-.32c-.608 2.272-2.592 3.712-6.08 3.712Zm.352-4.8c0 .512.512.608 1.44.608 2.912 0 4.096-.352 4.256-1.792l-4.928.576c-.544.064-.768.256-.768.608Zm17.015 9.92h-2.912V241h4.672c.48 0 .864-.064 1.12-.192l-7.712-15.936h6.848l2.784 6.336 1.152 3.712h.416l1.056-3.776 2.4-6.272h6.72l-7.648 16.768c-1.728 3.808-4.192 4.8-8.896 4.8Zm22.83-5.44h-5.632v-21.44h6.016v9.472h.32c.48-2.688 2.272-4.48 6.08-4.48 4.736 0 7.392 3.136 7.392 8.384 0 5.216-2.72 8.384-7.648 8.384-3.776 0-5.696-1.472-6.208-4.672h-.32V241Zm.384-8.032c0 2.336 1.28 2.848 3.968 2.848 2.784 0 3.744-.736 3.744-2.88s-.96-2.912-3.744-2.912c-2.688 0-3.968.448-3.968 2.72v.224Zm24.318 8.352c-5.6 0-9.408-3.168-9.408-8.384 0-5.248 3.808-8.384 9.408-8.384s9.408 3.136 9.408 8.384c0 5.216-3.808 8.384-9.408 8.384Zm0-5.312c2.72 0 3.456-.736 3.456-3.072 0-2.336-.736-3.104-3.456-3.104-2.72 0-3.456.768-3.456 3.104 0 2.336.736 3.072 3.456 3.072ZM290.35 241h-7.264l6.016-7.808v-.32l-6.016-8h7.36l3.296 4.864h.32l3.168-4.864h7.264l-6.016 7.904v.32l6.016 7.904h-7.36l-3.296-4.736h-.32L290.35 241Zm-81.862 25.655h-.768v-8.04h.852v3.768h.06c.252-1.02 1.104-1.776 2.532-1.776 1.86 0 2.88 1.284 2.88 3.084s-1.02 3.084-2.952 3.084c-1.32 0-2.268-.684-2.544-1.872h-.06v1.752Zm.084-2.868c0 1.404.888 2.208 2.316 2.208 1.416 0 2.292-.588 2.292-2.304 0-1.716-.9-2.292-2.268-2.292-1.5 0-2.34.816-2.34 2.28v.108Zm7.299 4.908h-.744v-.78h.9c.6 0 .828-.18 1.056-.672l.288-.6-2.94-5.916h.936l1.74 3.528.66 1.452h.072l.636-1.464 1.644-3.516h.936l-3.216 6.756c-.432.912-.984 1.212-1.968 1.212Zm14.16-2.04h-5.952v-8.04h.852v7.26h5.1v.78Zm1.814-6.684h-.852v-1.356h.852v1.356Zm0 6.684h-.852v-5.928h.852v5.928Zm2.202 0h-.768v-8.04h.852v3.768h.06c.252-1.02 1.104-1.776 2.532-1.776 1.86 0 2.88 1.284 2.88 3.084s-1.02 3.084-2.952 3.084c-1.32 0-2.268-.684-2.544-1.872h-.06v1.752Zm.084-2.868c0 1.404.888 2.208 2.316 2.208 1.416 0 2.292-.588 2.292-2.304 0-1.716-.9-2.292-2.268-2.292-1.5 0-2.34.816-2.34 2.28v.108Zm9.339 2.988c-1.908 0-3.12-1.2-3.12-3.084 0-1.8 1.2-3.084 3.108-3.084 1.74 0 2.976 1.008 2.976 2.772 0 .216-.024.396-.06.552h-5.22c.048 1.332.732 2.136 2.304 2.136 1.392 0 2.04-.516 2.04-1.38v-.084h.852v.084c0 1.236-1.224 2.088-2.88 2.088Zm-.024-5.46c-1.536 0-2.232.792-2.292 2.088h4.476v-.18c0-1.248-.792-1.908-2.184-1.908Zm4.97 5.34h-.852v-5.928h.768v1.62h.06c.18-.948.852-1.74 2.088-1.74 1.368 0 1.968 1.008 1.968 2.112v.588h-.852v-.456c0-1.008-.42-1.5-1.452-1.5-1.2 0-1.728.756-1.728 2.088v3.216Zm8.655 0h-1.284c-1.176 0-1.932-.492-1.932-1.884v-3.312h-1.056v-.732h1.056v-1.416h.864v1.416h2.352v.732h-2.352v3.36c0 .828.408 1.056 1.272 1.056h1.08v.78Zm1.714 2.04h-.744v-.78h.9c.6 0 .828-.18 1.056-.672l.288-.6-2.94-5.916h.936l1.74 3.528.66 1.452h.072l.636-1.464 1.644-3.516h.936l-3.216 6.756c-.432.912-.984 1.212-1.968 1.212Zm6.857-2.04h-.852v-8.04h3.696c1.74 0 2.964.996 2.964 2.712 0 1.728-1.224 2.724-2.964 2.724h-2.844v2.604Zm2.772-7.26h-2.772v3.876h2.772c1.416 0 2.172-.576 2.172-1.944 0-1.344-.756-1.932-2.172-1.932Zm5.602 7.38c-1.164 0-1.956-.552-1.956-1.512 0-.972.804-1.392 1.908-1.512l2.82-.312v-.456c0-1.152-.504-1.608-1.8-1.608-1.272 0-1.944.456-1.944 1.5v.048h-.852v-.048c0-1.248 1.032-2.268 2.856-2.268 1.8 0 2.568 1.032 2.568 2.34v3.708h-.768v-1.596h-.06c-.348 1.092-1.392 1.716-2.772 1.716Zm-1.104-1.572c0 .6.396.912 1.296.912 1.44 0 2.58-.636 2.58-2.064v-.048l-2.556.288c-.888.084-1.32.3-1.32.912Zm6.885 3.492h-.744v-.78h.9c.6 0 .828-.18 1.056-.672l.288-.6-2.94-5.916h.936l1.74 3.528.66 1.452h.072l.636-1.464 1.644-3.516h.936l-3.216 6.756c-.432.912-.984 1.212-1.968 1.212Z"
        fill="#fff"
        initial={{ rotate: 0 }}
        transition={{ duration: 10, repeat: Infinity, ease: 'linear' }}
      />
    </motion.svg>
  );
}
