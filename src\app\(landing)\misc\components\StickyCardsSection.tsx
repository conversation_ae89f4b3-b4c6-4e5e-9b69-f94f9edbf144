"use client"

import { useScroll, motion } from 'framer-motion';
import { useRef } from 'react';
import StackingCard from './StickyCard';


export const projects = [
    {
        title: "Savings options",
        content: "Cultivate a positive savings culture with the Seeds app. Save individually or join forces with friends and family to boost everyone’s savings capacity. Together, you’ll always have more than enough for life’s rainy days",
        link: "https://fedwk.libertyng.com/",
        cta_text: "Savings",
        link_text: "Learn more",
        bg_color: "#081934",
        text_color: "#1255C3",
        i: 0,
    },
    {
        title: "Loan options",
        content: "Access low-interest loans to grow your business or finance personal and business asset needs. Build a consistent savings pattern, maintain a strong financial flow, and establish a good credit record to unlock endless opportunities",
        link: "https://fedwk.libertyng.com/",
        cta_text: "Loans",
        link_text: "Get loan",
        bg_color: "#8D5604",
        text_color: "#E78D07",
        i: 1,
    },
    {
        title: "Affordable healthcare",
        content: "Get to enjoy affordable individual and family health care services when you save or take up loans on seeds ensuring a secured physical and financial health.",
        link: "https://fedwk.libertyng.com/",
        cta_text: "Health insurance",
        link_text: "Learn more",
        bg_color: "#264937",
        text_color: "#009948",
        i: 2,
    },
    {
        title: "Onlending partner",
        content: "Earn up to 20% annual returns by becoming a Seeds onlender. Provide financing for low-risk loans to borrowers effortlessly while growing your wealth in a secure and seamless way",
        link: "https://fedwk.libertyng.com/",
        cta_text: "Onlending",
        link_text: "Get loan",
        bg_color: "#4586e036",
        text_color: "#2973DD",
        i: 3,
    },
]



export default function StackingCardsSection() {
    const container = useRef(null);
    const { scrollYProgress } = useScroll({
        target: container,
        offset: ['start start', 'end end']
    })

    return (
        <main className="relative md:-mt-[15vh] flex flex-col" ref={container} >
            <motion.div className="sticky h-screen flex items-center justify-center top-0"
            // initial={{ top: "-105%", position: "relative" }}
            // animate={{ top: 0 }}
            // whileInView={{ top: "-" }}
            >
                <div className='max-md:hidden absolute top-[15%] flex items-center justify-between w-full max-w-[1050px] mx-auto'>

                    <motion.img
                        className='top-0 left-0'
                        src="/images/landing/STATiC_PHONE_ACCESSORY1.png"

                    />
                    <motion.img
                        className='top-0 left-0'
                        src="/images/landing/STATiC_PHONE_ACCESSORY2.png"

                    />
                </div>
                <div className='max-md:hidden absolute top-[25%] flex items-center justify-between w-full max-w-[850px] mx-auto'>

                    <motion.img
                        className='top-0 left-0'
                        src="/images/landing/STATiC_PHONE_ACCESSORY3.png"

                    />
                    <motion.img
                        className='top-0 left-0'
                        src="/images/landing/STATiC_PHONE_ACCESSORY4.png"

                    />
                </div>
                <motion.img
                    src="/images/landing/STATiC_PHONE.png"

                />
            </motion.div>
            {
                projects.map((project, i) => {
                    const targetScale = 1 - ((projects.length - i) * 0.05);
                    return <StackingCard key={`p_${i}`}
                        {...project} progress={scrollYProgress}

                        range={[i * .25, 1]} targetScale={targetScale}
                    />
                })
            }
        </main>
    )
}
