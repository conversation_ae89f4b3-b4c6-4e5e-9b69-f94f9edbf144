'use client'

import { LoaderModal, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/core';
import { TransactionsPageWrapper } from '../misc/components/TransactionsPageWrapper';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useTransactionHistory } from '../misc/api/getTransactionHistory';
import { addCommasToNumber, formatShortDateTime } from '@/utils';



const NetworkTypeOptions = [
    { name: 'Today', value: 'today' },
    { name: 'This week', value: 'this week' },
];


export default function Transactions() {
    const { emailAddress, phone_number } = useOnboardingPlan();

    const { data: transactionData, isLoading } = useTransactionHistory(emailAddress as string, phone_number as string);

    return (
        <>
            <LoaderModal
                isOpen={
                    isLoading
                }
            />

            <TransactionsPageWrapper heading={"Transaction"}>
                <div className='p-6  overflow-auto'>
                    <div className='w-full flex justify-between items-center'>
                        <p className='text-[#242424] font-semibold'>History</p>
                        <div className=''>
                            <Select >
                                <SelectTrigger className="bg-[#F1F8FF] text-[#032282]" id="network">
                                    <SelectValue className='text-[#032282]' placeholder="Sort by" />
                                </SelectTrigger>
                                <SelectContent className="text-[#032282]">
                                    {NetworkTypeOptions.map(({ name, value }) => {
                                        return (
                                            <SelectItem key={name} value={value}>
                                                {name}
                                            </SelectItem>
                                        );
                                    })}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>


                    <div className='mt-6'>
                        <div className=''>
                            {transactionData?.results.length === 0 ? (<>
                                <div className="flex items-center">
                                    <p className='font-medium text-[#646464] text-right text-sm'>No transaction history</p>
                                </div>
                            </>) : (<>
                                {transactionData?.results.map(({ amount, description, transaction_type, date_created, wallet_balance_after, wallet_balance_before, id }) => {
                                    const regex = /'([^']+)'/;

                                    const match = description.match(regex);
                                    const planName = match ? match[1] : null;

                                    return (
                                        <div className='flex items-center justify-between w-full pt-[20px] border-[#E9EBEE] border-b-[0.5px] pb-[14px]' key={id}>
                                            <div className='flex gap-[10px] items-center'>
                                                {transaction_type === "CREDIT" ?
                                                    <>
                                                        <svg fill="none" height="34" viewBox="0 0 34 34" width="34" xmlns="http://www.w3.org/2000/svg">
                                                            <rect fill="#E9FFF0" height="34" rx="17" width="34" />
                                                            <path d="M14.5 25.9582H19.5C24.025 25.9582 25.9584 24.0248 25.9584 19.4998V14.4998C25.9584 9.97484 24.025 8.0415 19.5 8.0415H14.5C9.97504 8.0415 8.04171 9.97484 8.04171 14.4998V19.4998C8.04171 24.0248 9.97504 25.9582 14.5 25.9582ZM19.5 9.2915C23.3417 9.2915 24.7084 10.6582 24.7084 14.4998V19.4998C24.7084 23.3415 23.3417 24.7082 19.5 24.7082H14.5C10.6584 24.7082 9.29171 23.3415 9.29171 19.4998V14.4998C9.29171 10.6582 10.6584 9.2915 14.5 9.2915H19.5Z" fill="#01AE53" />
                                                            <path d="M14.6418 18.7418H18.1752C18.5168 18.7418 18.8002 18.4585 18.8002 18.1168C18.8002 17.7752 18.5168 17.4918 18.1752 17.4918H15.2668V14.5835C15.2668 14.2418 14.9835 13.9585 14.6418 13.9585C14.3002 13.9585 14.0168 14.2418 14.0168 14.5835V18.1168C14.0168 18.4668 14.3002 18.7418 14.6418 18.7418Z" fill="#01AE53" />
                                                            <path d="M14.6417 18.7414C14.8001 18.7414 14.9584 18.6831 15.0834 18.5581L19.8001 13.8414C20.0417 13.5998 20.0417 13.1998 19.8001 12.9581C19.5584 12.7164 19.1584 12.7164 18.9167 12.9581L14.2001 17.6748C13.9584 17.9164 13.9584 18.3164 14.2001 18.5581C14.3251 18.6831 14.4834 18.7414 14.6417 18.7414Z" fill="#01AE53" />
                                                            <path d="M17 22.1916C18.7584 22.1916 20.525 21.9083 22.2 21.35C22.525 21.2416 22.7 20.8833 22.5917 20.5583C22.4834 20.2333 22.125 20.05 21.8 20.1666C18.7 21.2 15.2917 21.2 12.1917 20.1666C11.8667 20.0583 11.5084 20.2333 11.4 20.5583C11.2917 20.8833 11.4667 21.2416 11.7917 21.35C13.475 21.9166 15.2417 22.1916 17 22.1916Z" fill="#01AE53" />
                                                        </svg>
                                                    </> :
                                                    <>
                                                        <svg fill="none" height="34" viewBox="0 0 34 34" width="34" xmlns="http://www.w3.org/2000/svg">
                                                            <rect fill="#FFF2F2" height="34" rx="17" width="34" />
                                                            <path d="M19.5 25.9582H14.5C9.97496 25.9582 8.04163 24.0248 8.04163 19.4998V14.4998C8.04163 9.97484 9.97496 8.0415 14.5 8.0415H19.5C24.025 8.0415 25.9583 9.97484 25.9583 14.4998V19.4998C25.9583 24.0248 24.025 25.9582 19.5 25.9582ZM14.5 9.2915C10.6583 9.2915 9.29163 10.6582 9.29163 14.4998V19.4998C9.29163 23.3415 10.6583 24.7082 14.5 24.7082H19.5C23.3416 24.7082 24.7083 23.3415 24.7083 19.4998V14.4998C24.7083 10.6582 23.3416 9.2915 19.5 9.2915H14.5Z" fill="#FF0000" />
                                                            <path d="M19.3582 17.5665C19.0165 17.5665 18.7332 17.2832 18.7332 16.9415V14.0332H15.8248C15.4832 14.0332 15.1998 13.7499 15.1998 13.4082C15.1998 13.0665 15.4832 12.7832 15.8248 12.7832H19.3582C19.6998 12.7832 19.9832 13.0665 19.9832 13.4082V16.9415C19.9832 17.2832 19.6998 17.5665 19.3582 17.5665Z" fill="#FF0000" />
                                                            <path d="M14.6416 18.7414C14.4833 18.7414 14.3249 18.6831 14.1999 18.5581C13.9583 18.3164 13.9583 17.9164 14.1999 17.6748L18.9166 12.9581C19.1583 12.7164 19.5583 12.7164 19.7999 12.9581C20.0416 13.1998 20.0416 13.5998 19.7999 13.8414L15.0833 18.5581C14.9666 18.6831 14.7999 18.7414 14.6416 18.7414Z" fill="#FF0000" />
                                                            <path d="M17 22.1916C15.2416 22.1916 13.475 21.9083 11.8 21.35C11.475 21.2416 11.3 20.8833 11.4083 20.5583C11.5166 20.2333 11.875 20.05 12.2 20.1666C15.3 21.2 18.7083 21.2 21.8083 20.1666C22.1333 20.0583 22.4916 20.2333 22.6 20.5583C22.7083 20.8833 22.5333 21.2416 22.2083 21.35C20.525 21.9166 18.7583 22.1916 17 22.1916Z" fill="#FF0000" />
                                                        </svg>
                                                    </>}


                                                <div className='space-y-[2px]'>
                                                    <p className='text-[#242424] text-sm font-semibold'>{planName}</p>
                                                    <p className='text-xs font-medium text-[#646464]'>{formatShortDateTime(date_created, true)}</p>

                                                </div>
                                            </div>

                                            <div className='space-y-[2px]'>
                                                <p className='text-[#01AE53] text-sm font-semibold text-right'>+ ₦{addCommasToNumber(amount)}</p>
                                                <p className='text-xs font-medium text-[#646464] text-right'>Bal before: {addCommasToNumber(wallet_balance_before)}</p>
                                                <p className='text-xs font-medium text-[#646464] text-right'>Bal after: {addCommasToNumber(wallet_balance_after)}</p>
                                            </div>

                                        </div>
                                    )
                                })}</>)}
                        </div>
                    </div>
                </div>
            </TransactionsPageWrapper>
        </>
    );
}
