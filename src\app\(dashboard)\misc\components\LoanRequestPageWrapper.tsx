import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';

import { cn } from '@/utils/classNames';

// import { LendingOptions } from './modals/LendingOptions';
// import { useBooleanStateControl } from '@/hooks';
import { Button } from '@/components/core';
import { useRouter } from 'next/navigation';
import { StartLending } from './startLending';

export function LoanRequestPageWrapper({
  children,
  heading,
  isCentered = false,
}: {
  children: React.ReactNode;
  heading: React.ReactNode;
  isCentered?: boolean;
}) {
  const router = useRouter()

  return (
    <>
      <main className="relative mx-auto max-w-[34.375rem]  md:h-auto md:min-h-0 md:w-full md:grow-0 md:overflow-y-auto ">
        <div className="relative md:h-[812px]  py-5 ">
          <div
            className={cn(
              'absolute inset-0 rounded-[5px] bg-[#F1F8FF]'
            )}
          />
          <h1
            className={cn(
              'relative flex gap-2 px-6 items-center mb-1 font-clash text-xl font-semibold leading-[normal] text-[#032282]',
              isCentered && 'text-center'
            )}
          >
            <Button className='p-0 cursor-pointer' variant="unstyled" onClick={() => {
              router.back()
            }}>
              <svg fill="none" height="34" viewBox="0 0 34 34" width="34" xmlns="http://www.w3.org/2000/svg">
                <rect height="32" rx="16" stroke="#032282" width="32" x="1" y="1" />
                <mask height="32" id="mask0_17281_15170" maskUnits="userSpaceOnUse" width="32" x="1" y="1">
                  <rect fill="white" height="32" rx="16" width="32" x="1" y="1" />
                </mask>
                <g mask="url(#mask0_17281_15170)">
                  <path clipRule="evenodd" d="M14.1716 19.8284C12.6615 18.3184 12.6112 15.9014 14.0206 14.3309L14.1716 14.1716L18.291 10.2929C18.6815 9.90237 19.3147 9.90237 19.7052 10.2929C20.0657 10.6534 20.0934 11.2206 19.7884 11.6129L19.7052 11.7071L15.5858 15.5858C14.8458 16.3257 14.8069 17.5012 15.469 18.287L15.5858 18.4142L19.7052 22.2929C20.0958 22.6834 20.0958 23.3166 19.7052 23.7071C19.3447 24.0676 18.7775 24.0953 18.3852 23.7903L18.291 23.7071L14.1716 19.8284Z" fill="#032282" fillRule="evenodd" />
                </g>
              </svg>
            </Button>


            <Balancer className={cn(isCentered && 'text-center')}>
              {heading}
            </Balancer>
          </h1>
          <div className='px-6 pb-3 mt-5 relative max-w-[522px] text-[#242424] leading-[22px] text-sm font-medium'>

            Here are prospective borrowers who have made loan requests but are yet to get loans disbursed to them. You can facilitate this loan requests by clicking on the <span className='text-[#032282] font-bold'>“Start lending”</span> button below and earn up to <span className='text-[#032282] font-bold'>20%</span> interest.
          </div>

          <div className='bg-white relative'>
            {children}
          </div>



          <StartLending />

        </div>
      </main >
    </>
  );
}
