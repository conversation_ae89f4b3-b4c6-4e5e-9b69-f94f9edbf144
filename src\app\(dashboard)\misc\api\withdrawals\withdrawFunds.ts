import { savingsAxios } from '@/lib/axios';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

import { useMutation } from 'react-query';

import type { AxiosResponse } from 'axios';


export interface WithdrawFundsDTO {
  phone_number?: string;
  plan_id?: string;
  wallet_type: string;
  amount?: string;
}

export interface CreatePlanOTPResponse {
  message: string;
}

const withdrawFunds = (withdrawFundsDTO: WithdrawFundsDTO,  email: string): Promise<AxiosResponse> => {
  const headers = {
    'Email-Address': email, 
    'Authorization':`Bearer ${tokenStorage.getToken()}`
  }; 
  
  
  return savingsAxios.post(`/ajo/agent/payment/withdrawal-account/withdraw/`, withdrawFundsDTO, {headers});
};

export const useWithdrawFunds = (email: string) => {
  return useMutation('withdrawFunds', (withdrawFundsDTO: WithdrawFundsDTO) => withdrawFunds(withdrawFundsDTO, email), {});
};
