"use client"

import { motion, useInView } from "framer-motion"

import NumberF<PERSON> from '@number-flow/react'
import { <PERSON><PERSON>, <PERSON> } from "@/components/core"

import { MapIcon, MoneysIncon, Profile2User, ShieldIcon } from "../icons"
import React from "react"




interface StatCardProps {
    icon: React.ReactNode
    value: string
    label: string
    delay?: number
    suffix?: string
}

export function StatCard({ icon, value, label, delay = 0, suffix = "" }: StatCardProps) {
    const ref = React.useRef(null)
    const isInView = useInView(ref, { once: false, amount: 0.5 })
    const [shouldAnimate, setShouldAnimate] = React.useState(false)

    const numericValue = parseInt(value.replace(/[^0-9]/g, ''))
    const isThousands = value.toLowerCase().includes('k')

    React.useEffect(() => {
        if (isInView) {
            setShouldAnimate(true)
        }
        else{
            setShouldAnimate(false)
        }
    }, [isInView])

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="h-full"
            initial={{ opacity: 0, y: 20 }}
            ref={ref}
            transition={{ duration: 0.5, delay }}
            whileHover={{ scale: 1.02 }}
        >
            <Card className="flex flex-col p-6 bg-[#000619] border-[#1a1b3e] text-white h-full rounded-2xl">
                <div className="mb-4 p-2 max-w-max rounded-full bg-[#FFFFFF0D]">{icon}</div>
                <div className="flex items-center text-4xl lg:text-6xl font-bold mb-2 mt-auto">
                    <NumberFlow
                        // delay={delay * 1000}
                        // duration={1500}
                        format={{
                            notation: isThousands ? "compact" : "standard",
                            compactDisplay: "short",
                        }}
                        
                        // value={isThousands ? numericValue * 1000 : numericValue}
                        value={shouldAnimate ? (isThousands ? numericValue * 1000 : numericValue) : 0}
                    />
                    {suffix && <span className="text-4xl lg:text-6xl font-bold ml-1">{suffix}</span>}
                    {isThousands && <span className="text-2xl lg:text-4xl ml-1">k+</span>}
                </div>
                <p className="text-gray-400 text-lg">{label}</p>
            </Card>
        </motion.div>
    )
}



export function YAxis() {
    const values = [25000, 20000, 18000, 15000, 12000, 9000, 6000, 3000]

    return (
        <div className="absolute left-0 h-full flex flex-col justify-between text-xs md:text-sm text-gray-500">
            {values.map((value) => (
                <div className="relative -left-2" key={value} >
                    {value}
                </div>
            ))}
        </div>
    )
}




interface AnimatedBarProps {
    height: number
    value: string
    month: string
    delay?: number
    indicatorColor?: string
}

export function AnimatedBar({
    height,
    value,
    month,
    delay = 0,
    indicatorColor = "#3B82F6"
}: AnimatedBarProps) {
    return (
        <div className="flex flex-col items-center">
            <div className="h-[300px] flex flex-col items-center justify-end mb-2">
                <div className="text-white text-[0.7rem] md:text-sm ">{value}</div>
                <div
                    className="w-[90%] max-w-6 h-1 rounded-full mb-2 mx-auto"
                    style={{ backgroundColor: indicatorColor }}
                />
                <motion.div
                    // animate={{ height: `${height}%` }}
                    className="w-8 md:w-12 relative"
                    initial={{ height: 0 }}
                    transition={{ duration: 0.5, delay, ease: "linear" }}
                    whileInView={{ height: `${height}%` }}
                >
                    <div className="absolute inset-0 rounded-lg bg-gradient-to-b from-[#131428] to-[#0D0E23]" />
                </motion.div>
            </div>
            <div className="text-gray-400 text-sm font-medium">{month}</div>
        </div>
    )
}



const chartData = [
    { month: "Jun", value: "40M", height: 10, color: "#3B82F6" },
    { month: "Jul", value: "62M", height: 15, color: "#F59E0B" },
    { month: "Aug", value: "120M", height: 25, color: "#3B82F6" },
    { month: "Sep", value: "290M", height: 45, color: "#1766D8" },
    { month: "Oct", value: "430M", height: 60, color: "#3B82F6" },
    { month: "Nov", value: "600M", height: 75, color: "#099976" },
    { month: "Dec", value: "1.1B", height: 95, color: "#009948" }
]

export default function JourneySection() {
    return (
        <section className="grid min-h-screen bg-[#000619] py-16 px-4 md:px-8">
            <div className="w-full max-w-7xl mx-auto place-content-center">
                <div className="flex flex-wrap items-center gap-5 mb-16">
                    <motion.h2
                        animate={{ opacity: 1, x: 0 }}
                        className="text-4xl md:text-5xl font-medium text-white"
                        initial={{ opacity: 0, x: -20 }}
                    >
                        Our journey so far...
                    </motion.h2>
                    <motion.div
                        animate={{ opacity: 1, x: 0 }}
                        initial={{ opacity: 0, x: 20 }}
                        transition={{ delay: 0.3 }}
                    >
                        <Button
                            className=" text-[#2073FA] bg-[#1255C333] text-base"
                            variant="default"
                        >
                            Become an onlender
                        </Button>
                    </motion.div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-[0.5fr,1fr,0.5fr] items-stretch gap-6 mb-12">
                    <section className="grid grid-rows-2 gap-5 md:gap-8">

                        <StatCard
                            delay={0.2}
                            icon={<ShieldIcon className="w-6 md:w-8 h-6 md:h-8" />}
                            label="Saved"
                            suffix="M"
                            value="570"

                        />
                        <StatCard
                            delay={0.4}
                            icon={<MoneysIncon className="w-6 md:w-8 h-6 md:h-8" />}
                            label="Processed loans"
                            suffix="k+"
                            value="25"
                        />
                    </section>

                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-[#000619] p-8 rounded-2xl relative border border-[#1a1b3e]"
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ delay: 0.15 }}
                    >
                        <div className="absolute top-8 left-8">
                            <h3 className="text-4xl md:text-5xl font-bold text-white mb-2">1.1Billion</h3>
                            <p className="text-gray-400 text-xl">Disbursed</p>
                        </div>
                        <div className="pt-32">
                            <div className="relative pl-10 md:pl-16">
                                <YAxis />
                                <div className="flex justify-between items-end">
                                    {chartData.map((data, index) => (
                                        <AnimatedBar
                                            delay={0.01 + index * 0.005}
                                            height={data.height}
                                            indicatorColor={data.color}
                                            key={data.month}
                                            month={data.month}
                                            value={data.value}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    </motion.div>

                    <section className="grid grid-rows-2 gap-5 md:gap-8">
                        <StatCard
                            delay={0.6}
                            icon={<Profile2User className="w-6 md:w-8 h-6 md:h-8" />}
                            label="Loan officers"
                            suffix="+"
                            value="200"
                        />
                        <StatCard
                            delay={0.8}
                            icon={<MapIcon className="w-6 md:w-8 h-6 md:h-8" />}
                            label="Regions covered"
                            value="24"

                        />
                    </section>
                </div>


            </div>
        </section>
    )
}

