'use client'

// import { Button } from '@/components/core';
// import { LoaderModal } from '@/components/core';

import { useUser } from '@/app/(auth)/(onboarding)/misc/api';
import { ProfilePageWrapper } from '../misc/components/ProfilePageWrapper';
import Link from 'next/link';
import { useOnboardingPlan } from '@/store/onBoardingPlan';
import { useVirtualWallet } from '../misc/api/getVirtualWallet';



export default function Transactions() {
    const { emailAddress } = useOnboardingPlan();

    const { data } = useVirtualWallet(emailAddress as string);

    const {
        data: viewUserData } = useUser();

    const { first_name, last_name, email } = viewUserData?.user_data || {};


    return (
        <>
            {/* <LoaderModal
                isOpen={
                    isLoading
                }
            /> */}

            <ProfilePageWrapper heading={"Profile"}>
                <div className='py-3 md:px-4 overflow-auto'>
                    <div className='py-[18px] px-5 rounded-10 flex items-center gap-[14px] bg-white'>
                        <svg fill="none" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24" fill="#032282" r="24" />
                            <path d="M24.1601 22.87C24.0601 22.86 23.9401 22.86 23.8301 22.87C21.4501 22.79 19.5601 20.84 19.5601 18.44C19.5601 15.99 21.5401 14 24.0001 14C26.4501 14 28.4401 15.99 28.4401 18.44C28.4301 20.84 26.5401 22.79 24.1601 22.87Z" stroke="white" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                            <path d="M19.16 26.56C16.74 28.18 16.74 30.82 19.16 32.43C21.91 34.27 26.42 34.27 29.17 32.43C31.59 30.81 31.59 28.17 29.17 26.56C26.43 24.73 21.92 24.73 19.16 26.56Z" stroke="white" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                        </svg>
                        <div>
                            <h3 className='text-[#333333] font-semibold mb-[3px]'>{first_name + " " + last_name || ""}</h3>
                            <p className='text-[#1976D2] text-[13px]'>{email}</p>
                        </div>
                    </div>


                    <div className='py-[18px] space-y-[17px] mt-4 px-5 rounded-10 gap-[14px] bg-white'>

                        <div className='w-full gap-[81px] flex items-center'>
                            <div>
                                <h3 className='text-[#556575] font-normal text-[13px] md:text-sm mb-[3px]'>Account name</h3>
                                <p className='text-xs md:text-[14px] font-medium'>{data?.data.account_name}</p>
                            </div>

                            <div>
                                <h3 className='text-[#556575] text-[13px] md:text-sm font-normal mb-[3px]'>Account number</h3>
                                <p className='text-xs md:text-[14px] font-medium'>{data?.data.account_number}</p>
                            </div>
                        </div>

                        <div className='w-full gap-[40px] flex items-center'>
                            <div>
                                <h3 className='text-[#556575] text-[13px] md:text-sm font-normal mb-[3px]'>Bank name</h3>
                                <p className='text-xs md:text-[14px] font-medium'>{data?.data.bank_name}</p>
                            </div>

                            {/* <div>
                                <Button className='bg-[#F5FAFF] text-[#1976D2]'>Change</Button>
                            </div> */}
                        </div>
                    </div>

                    <div className='py-4 space-y-[17px] mt-4 px-5 rounded-10 gap-[14px] bg-white'>
                        <Link className='flex items-center justify-between' href='/profile/profile-details'>
                            <div className='flex items-center gap-4'>
                                <svg fill="none" height="44" viewBox="0 0 44 44" width="44" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="22" cy="22" fill="#F1F8FF" r="22" />
                                    <path d="M22.1334 21.0587C22.05 21.0503 21.95 21.0503 21.8584 21.0587C19.875 20.992 18.3 19.367 18.3 17.367C18.3 15.3253 19.95 13.667 22 13.667C24.0417 13.667 25.7001 15.3253 25.7001 17.367C25.6917 19.367 24.1167 20.992 22.1334 21.0587Z" stroke="#032282" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                                    <path d="M17.9666 24.133C15.9499 25.483 15.9499 27.683 17.9666 29.0247C20.2583 30.558 24.0166 30.558 26.3083 29.0247C28.3249 27.6747 28.3249 25.4747 26.3083 24.133C24.0249 22.608 20.2666 22.608 17.9666 24.133Z" stroke="#032282" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                                </svg>
                                <p className='text-sm'>Profile details</p>
                            </div>

                            <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                            </svg>
                        </Link>
                        <Link className='flex items-center justify-between'
                            href="/profile/next-of-kin"
                        //  href={`sign-up/next-of-kin?email=${email}&phone=${phone_number}`}
                        >
                            <div className='flex items-center gap-4'>
                                <svg fill="none" height="44" viewBox="0 0 44 44" width="44" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="22" cy="22" fill="#F1F8FF" r="22" />
                                    <path d="M21.9999 22.0003C24.3011 22.0003 26.1666 20.1348 26.1666 17.8337C26.1666 15.5325 24.3011 13.667 21.9999 13.667C19.6987 13.667 17.8333 15.5325 17.8333 17.8337C17.8333 20.1348 19.6987 22.0003 21.9999 22.0003Z" stroke="#032282" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                                    <path d="M14.8416 30.3333C14.8416 27.1083 18.0499 24.5 21.9999 24.5C22.7999 24.5 23.5749 24.6083 24.2999 24.8083" stroke="#032282" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                                    <path d="M30.3334 27.0003C30.3334 27.6253 30.1584 28.217 29.8501 28.717C29.6751 29.017 29.4501 29.2837 29.1918 29.5003C28.6084 30.0253 27.8417 30.3337 27.0001 30.3337C25.7834 30.3337 24.7251 29.6837 24.1501 28.717C23.8417 28.217 23.6667 27.6253 23.6667 27.0003C23.6667 25.9503 24.1501 25.0087 24.9167 24.4003C25.4917 23.942 26.2167 23.667 27.0001 23.667C28.8417 23.667 30.3334 25.1587 30.3334 27.0003Z" stroke="#032282" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                                    <path d="M25.7002 27.0002L26.5252 27.8252L28.3002 26.1836" stroke="#032282" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                                </svg>

                                <p className='text-sm'>Next of kin</p>
                            </div>

                            <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                            </svg>
                        </Link>
                        <Link className='flex items-center justify-between' href='/'>
                            <div className='flex items-center gap-4'>
                                <svg fill="none" height="44" viewBox="0 0 44 44" width="44" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="22" cy="22" fill="#F1F8FF" r="22" />
                                    <path d="M17.1501 30.9579C17.0668 30.9579 16.9751 30.9496 16.9001 30.9413L15.0918 30.6913C14.2251 30.5746 13.4418 29.7996 13.3084 28.9163L13.0584 27.0913C12.9751 26.5079 13.2251 25.7496 13.6418 25.3246L17.3001 21.6663C16.7084 19.2996 17.3918 16.7996 19.1334 15.0746C21.8334 12.3829 26.2251 12.3746 28.9334 15.0746C30.2418 16.3829 30.9584 18.1246 30.9584 19.9746C30.9584 21.8246 30.2418 23.5663 28.9334 24.8746C27.1834 26.6079 24.6918 27.2913 22.3418 26.6913L18.6751 30.3496C18.3251 30.7163 17.7001 30.9579 17.1501 30.9579ZM24.0251 14.2996C22.5668 14.2996 21.1168 14.8496 20.0084 15.9579C18.5084 17.4496 17.9668 19.6329 18.5918 21.6663C18.6584 21.8913 18.6001 22.1246 18.4334 22.2913L14.5168 26.2079C14.3751 26.3496 14.2584 26.7163 14.2834 26.9079L14.5334 28.7329C14.5834 29.0496 14.9251 29.4079 15.2418 29.4496L17.0584 29.6996C17.2584 29.7329 17.6251 29.6163 17.7668 29.4746L21.7001 25.5496C21.8668 25.3829 22.1084 25.3329 22.3251 25.3996C24.3334 26.0329 26.5251 25.4913 28.0251 23.9913C29.0918 22.9246 29.6834 21.4913 29.6834 19.9746C29.6834 18.4496 29.0918 17.0246 28.0251 15.9579C26.9418 14.8579 25.4834 14.2996 24.0251 14.2996Z" fill="#032282" />
                                    <path d="M19.6582 29.1167C19.4999 29.1167 19.3416 29.0584 19.2166 28.9334L17.2999 27.0167C17.0582 26.7751 17.0582 26.3751 17.2999 26.1334C17.5416 25.8917 17.9416 25.8917 18.1832 26.1334L20.0999 28.0501C20.3416 28.2917 20.3416 28.6917 20.0999 28.9334C19.9749 29.0584 19.8166 29.1167 19.6582 29.1167Z" fill="#032282" />
                                    <path d="M24.0833 21.792C23.0499 21.792 22.2083 20.9503 22.2083 19.917C22.2083 18.8837 23.0499 18.042 24.0833 18.042C25.1166 18.042 25.9583 18.8837 25.9583 19.917C25.9583 20.9503 25.1166 21.792 24.0833 21.792ZM24.0833 19.292C23.7416 19.292 23.4583 19.5753 23.4583 19.917C23.4583 20.2587 23.7416 20.542 24.0833 20.542C24.4249 20.542 24.7083 20.2587 24.7083 19.917C24.7083 19.5753 24.4249 19.292 24.0833 19.292Z" fill="#032282" />
                                </svg>


                                <p className='text-sm'>Change password</p>
                            </div>

                            <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                            </svg>
                        </Link>
                        <Link className='flex items-center justify-between' href='/'>
                            <div className='flex items-center gap-4'>
                                <svg fill="none" height="43" viewBox="0 0 44 43" width="44" xmlns="http://www.w3.org/2000/svg">
                                    <ellipse cx="22" cy="21.4474" fill="#F1F8FF" rx="22" ry="21.4474" />
                                    <path d="M19.99 32.78C19.39 32.78 18.82 32.48 18.43 31.95L17.23 30.35C17.23 30.36 17.18 30.33 17.16 30.33H16.79C13.37 30.33 11.25 29.4 11.25 24.79V20.79C11.25 16.58 13.82 15.48 15.98 15.29C16.22 15.26 16.5 15.25 16.79 15.25H23.19C26.81 15.25 28.73 17.17 28.73 20.79V24.79C28.73 25.08 28.72 25.36 28.68 25.63C28.5 27.76 27.4 30.33 23.19 30.33H22.79L21.55 31.95C21.16 32.48 20.59 32.78 19.99 32.78ZM16.79 16.75C16.56 16.75 16.34 16.76 16.13 16.78C13.81 16.98 12.75 18.25 12.75 20.79V24.79C12.75 28.22 13.81 28.83 16.79 28.83H17.19C17.64 28.83 18.15 29.08 18.43 29.44L19.63 31.05C19.85 31.35 20.13 31.35 20.35 31.05L21.55 29.45C21.84 29.06 22.3 28.83 22.79 28.83H23.19C25.73 28.83 27 27.76 27.19 25.48C27.22 25.24 27.23 25.02 27.23 24.79V20.79C27.23 18 25.98 16.75 23.19 16.75H16.79Z" fill="#032282" />
                                    <path d="M19.9902 24.1904C19.4302 24.1904 18.9902 23.7404 18.9902 23.1904C18.9902 22.6404 19.4402 22.1904 19.9902 22.1904C20.5402 22.1904 20.9902 22.6404 20.9902 23.1904C20.9902 23.7404 20.5502 24.1904 19.9902 24.1904Z" fill="#032282" />
                                    <path d="M23.1899 24.1904C22.6299 24.1904 22.1899 23.7404 22.1899 23.1904C22.1899 22.6404 22.6399 22.1904 23.1899 22.1904C23.7399 22.1904 24.1899 22.6404 24.1899 23.1904C24.1899 23.7404 23.7399 24.1904 23.1899 24.1904Z" fill="#032282" />
                                    <path d="M16.7998 24.1904C16.2398 24.1904 15.7998 23.7404 15.7998 23.1904C15.7998 22.6404 16.2498 22.1904 16.7998 22.1904C17.3498 22.1904 17.7998 22.6404 17.7998 23.1904C17.7998 23.7404 17.3498 24.1904 16.7998 24.1904Z" fill="#032282" />
                                    <path d="M27.9401 26.29C27.7401 26.29 27.5401 26.21 27.4001 26.06C27.2401 25.9 27.1701 25.67 27.2001 25.45C27.2301 25.24 27.2401 25.02 27.2401 24.79V20.79C27.2401 18 25.9901 16.75 23.2001 16.75H16.8001C16.5701 16.75 16.3501 16.76 16.1401 16.78C15.9201 16.81 15.6901 16.73 15.5301 16.58C15.3701 16.42 15.2801 16.2 15.3001 15.98C15.4801 13.82 16.5901 11.25 20.8001 11.25H27.2001C30.8201 11.25 32.7401 13.17 32.7401 16.79V20.79C32.7401 25 30.1701 26.1 28.0101 26.29C27.9801 26.29 27.9601 26.29 27.9401 26.29ZM16.9201 15.25H23.1901C26.8101 15.25 28.7301 17.17 28.7301 20.79V24.66C30.4301 24.24 31.2301 22.99 31.2301 20.79V16.79C31.2301 14 29.9801 12.75 27.1901 12.75H20.7901C18.5901 12.75 17.3501 13.55 16.9201 15.25Z" fill="#032282" />
                                </svg>


                                <p className='text-sm'>Help and support</p>
                            </div>

                            <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                            </svg>
                        </Link>
                        <Link className='flex items-center justify-between' href='/'>
                            <div className='flex items-center gap-4'>
                                <svg fill="none" height="43" viewBox="0 0 44 43" width="44" xmlns="http://www.w3.org/2000/svg">
                                    <ellipse cx="22" cy="21.4474" fill="#F1F8FF" rx="22" ry="21.4474" />
                                    <path d="M29.42 20.75H26C25.59 20.75 25.25 20.41 25.25 20V13.01C25.25 12.27 25.54 11.58 26.06 11.06C26.58 10.54 27.27 10.25 28.01 10.25H28.02C29.27 10.26 30.45 10.75 31.35 11.64C32.25 12.55 32.74 13.75 32.74 15V17.42C32.75 19.41 31.41 20.75 29.42 20.75ZM26.75 19.25H29.42C30.58 19.25 31.25 18.58 31.25 17.42V15C31.25 14.14 30.91 13.32 30.3 12.7C29.69 12.1 28.87 11.76 28.02 11.75C28.02 11.75 28.02 11.75 28.01 11.75C27.68 11.75 27.36 11.88 27.12 12.12C26.88 12.36 26.75 12.67 26.75 13.01V19.25Z" fill="#032282" />
                                    <path d="M19 32.33C18.53 32.33 18.09 32.15 17.76 31.81L16.1 30.14C16.01 30.05 15.87 30.04 15.77 30.12L14.05 31.4C13.52 31.8 12.82 31.87 12.22 31.57C11.62 31.27 11.25 30.67 11.25 30V15C11.25 11.98 12.98 10.25 16 10.25H28C28.41 10.25 28.75 10.59 28.75 11C28.75 11.41 28.41 11.75 28 11.75C27.31 11.75 26.75 12.31 26.75 13V30C26.75 30.67 26.38 31.27 25.78 31.57C25.18 31.87 24.48 31.81 23.95 31.41L22.24 30.13C22.14 30.05 22 30.07 21.92 30.15L20.24 31.83C19.91 32.15 19.47 32.33 19 32.33ZM15.91 28.57C16.37 28.57 16.82 28.74 17.16 29.09L18.82 30.76C18.88 30.82 18.96 30.83 19 30.83C19.04 30.83 19.12 30.82 19.18 30.76L20.86 29.08C21.48 28.46 22.46 28.4 23.15 28.93L24.85 30.2C24.96 30.28 25.06 30.25 25.11 30.22C25.16 30.19 25.25 30.13 25.25 30V13C25.25 12.55 25.36 12.12 25.55 11.75H16C13.78 11.75 12.75 12.78 12.75 15V30C12.75 30.14 12.84 30.2 12.89 30.23C12.95 30.26 13.05 30.28 13.15 30.2L14.86 28.92C15.17 28.69 15.54 28.57 15.91 28.57Z" fill="#032282" />
                                    <path d="M22 22.7598H19C18.59 22.7598 18.25 22.4198 18.25 22.0098C18.25 21.5998 18.59 21.2598 19 21.2598H22C22.41 21.2598 22.75 21.5998 22.75 22.0098C22.75 22.4198 22.41 22.7598 22 22.7598Z" fill="#032282" />
                                    <path d="M22 18.7598H19C18.59 18.7598 18.25 18.4198 18.25 18.0098C18.25 17.5998 18.59 17.2598 19 17.2598H22C22.41 17.2598 22.75 17.5998 22.75 18.0098C22.75 18.4198 22.41 18.7598 22 18.7598Z" fill="#032282" />
                                    <path d="M15.9702 19.0098C15.4202 19.0098 14.9702 18.5598 14.9702 18.0098C14.9702 17.4598 15.4202 17.0098 15.9702 17.0098C16.5202 17.0098 16.9702 17.4598 16.9702 18.0098C16.9702 18.5598 16.5202 19.0098 15.9702 19.0098Z" fill="#032282" />
                                    <path d="M15.9702 23.0098C15.4202 23.0098 14.9702 22.5598 14.9702 22.0098C14.9702 21.4598 15.4202 21.0098 15.9702 21.0098C16.5202 21.0098 16.9702 21.4598 16.9702 22.0098C16.9702 22.5598 16.5202 23.0098 15.9702 23.0098Z" fill="#032282" />
                                </svg>



                                <p className='text-sm'>Terms and conditions</p>
                            </div>

                            <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                <path clipRule="evenodd" d="M11.5222 12.3567C12.7806 11.0983 12.8225 9.0842 11.6481 7.77546L11.5222 7.64265L8.08936 4.41042C7.76392 4.08498 7.23628 4.08498 6.91085 4.41042C6.61044 4.71082 6.58733 5.18352 6.84152 5.51042L6.91085 5.58893L10.3437 8.82116C10.9603 9.43778 10.9928 10.4174 10.4411 11.0722L10.3437 11.1782L6.91085 14.4104C6.58541 14.7359 6.58541 15.2635 6.91085 15.5889C7.21125 15.8893 7.68394 15.9124 8.01085 15.6583L8.08936 15.5889L11.5222 12.3567Z" fill="#646464" fillRule="evenodd" />
                            </svg>
                        </Link>

                    </div>


                </div>
            </ProfilePageWrapper>
        </>
    );
}
