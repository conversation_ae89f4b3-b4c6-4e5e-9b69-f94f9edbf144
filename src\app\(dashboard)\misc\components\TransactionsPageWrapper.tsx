import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';

import { cn } from '@/utils/classNames';
import { ButtonNavigation } from './ButtomNavigation';

export function TransactionsPageWrapper({
  children,
  heading,
  isCentered = false,
}: {
  children: React.ReactNode;
  heading: React.ReactNode;
  isCentered?: boolean;
}) {
  return (
    <>
      <main className="relative mx-auto max-w-[34.375rem] bg-white  md:h-auto md:min-h-0 md:w-full md:grow-0 md:overflow-y-auto ">
        <div className="relative h-screen md:h-[812px]  pb-5 ">
          <div
            className={cn(
              'absolute inset-0 rounded-[5px] bg-[#ffffff]'
            )}
          />
          <h1
            className={cn(
              'relative flex gap-2 items-center mb-1 font-clash text-xl font-semibold leading-[normal] px-4 py-7 text-white bg-[#032282]',
              isCentered && 'text-center'
            )}
          >
            <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
              <circle cx="16" cy="16" fill="white" r="16" />
              <path d="M16 24.0625C15.415 24.0625 14.845 23.7625 14.455 23.2375L13.6975 22.225C13.54 22.015 13.33 21.895 13.105 21.88C12.88 21.8725 12.655 21.97 12.475 22.1575L12.0475 21.775L12.46 22.1575C11.38 23.3125 10.5475 23.2225 10.15 23.065C9.745 22.9075 9.0625 22.39 9.0625 20.725V12.28C9.0625 8.95 10.0225 7.9375 13.165 7.9375H18.835C21.9775 7.9375 22.9375 8.95 22.9375 12.28V20.725C22.9375 22.3825 22.255 22.9 21.85 23.065C21.4525 23.2225 20.6275 23.3125 19.54 22.1575C19.36 21.9625 19.1425 21.8575 18.9025 21.88C18.6775 21.895 18.46 22.015 18.3025 22.225L17.545 23.2375C17.155 23.7625 16.585 24.0625 16 24.0625ZM13.06 20.7475C13.09 20.7475 13.1275 20.7475 13.1575 20.7475C13.7125 20.7775 14.2375 21.07 14.59 21.5425L15.3475 22.555C15.715 23.0425 16.2775 23.0425 16.645 22.555L17.4025 21.5425C17.7625 21.07 18.28 20.7775 18.8425 20.7475C19.3975 20.7175 19.9525 20.95 20.3575 21.385C20.9275 21.9925 21.3025 22.0675 21.43 22.015C21.61 21.94 21.805 21.505 21.805 20.725V12.28C21.805 9.5725 21.3325 9.0625 18.8275 9.0625H13.165C10.66 9.0625 10.1875 9.5725 10.1875 12.28V20.725C10.1875 21.5125 10.3825 21.9475 10.5625 22.015C10.69 22.0675 11.065 21.9925 11.635 21.385C12.04 20.9725 12.5425 20.7475 13.06 20.7475Z" fill="#032282" />
              <path d="M19 12.8125H13C12.6925 12.8125 12.4375 12.5575 12.4375 12.25C12.4375 11.9425 12.6925 11.6875 13 11.6875H19C19.3075 11.6875 19.5625 11.9425 19.5625 12.25C19.5625 12.5575 19.3075 12.8125 19 12.8125Z" fill="#032282" />
              <path d="M18.25 15.8125H13.75C13.4425 15.8125 13.1875 15.5575 13.1875 15.25C13.1875 14.9425 13.4425 14.6875 13.75 14.6875H18.25C18.5575 14.6875 18.8125 14.9425 18.8125 15.25C18.8125 15.5575 18.5575 15.8125 18.25 15.8125Z" fill="#032282" />
            </svg>


            <Balancer className={cn(isCentered && 'text-center')}>
              {heading}
            </Balancer>
          </h1>

          <div className='bg-white relative'>
            {children}
          </div>



          <ButtonNavigation />

        </div>


      </main >
    </>
  );
}
