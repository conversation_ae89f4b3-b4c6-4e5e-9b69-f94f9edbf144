import * as React from "react";
import { SVGProps } from "react";
const RightUpArrow = (props: SVGProps<SVGSVGElement>&{
    circleColor?: string;
}) => (
    <svg
        fill="none"
        height={44}
        viewBox="0 0 44 44"
        width={44}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <circle
            cx={22}
            cy={21.9998}
            fill={props.circleColor || "#032282"}
            r={15}
            transform="rotate(131.639 22 21.9998)"
        />
        <path
            d="M21.263 15.8806C21.1855 15.9678 21.1516 16.0938 21.1564 16.1761L21.8334 27.7057C21.848 27.9527 22.0223 28.1078 22.2694 28.0933C22.5165 28.0788 22.7102 27.8608 22.6957 27.6137L22.0187 16.0842C22.0042 15.8371 21.7862 15.6433 21.5392 15.6578C21.4181 15.7063 21.3406 15.7934 21.263 15.8806Z"
            fill="white"
        />
        <path
            d="M17.4498 23.418C17.2948 23.5924 17.2705 23.883 17.4885 24.0768L21.9352 28.0303C22.1096 28.1853 22.4002 28.2095 22.594 27.9916L26.5862 23.5013C26.7412 23.3269 26.7655 23.0363 26.5475 22.8425C26.3295 22.6487 26.0825 22.6632 25.8887 22.8812L22.2066 27.0227L18.065 23.3406C17.8907 23.1855 17.6048 23.2436 17.4498 23.418Z"
            fill="white"
        />
    </svg>
);
export default RightUpArrow;
