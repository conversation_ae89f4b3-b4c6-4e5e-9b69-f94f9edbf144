'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';

import type { AxiosError } from 'axios';
import React from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import {
  Button,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  FormError,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { SmallSpinner } from '@/icons/core';
import { cn } from '@/utils/classNames';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useFetchAccountName } from '@/app/(auth)/(onboarding)/misc/api/fetchAccountName';
import { useBanks } from '@/app/(auth)/(onboarding)/misc/api/getBanks';
import { EnterWithdrawalDetailsPinModal } from './EnterTransactionPin';


export interface BankLists {
  message: string;
  bank: BankEntity[];
}

export interface BankEntity {
  bank_code: string;
  cbn_code?: string;
  name: string;
  bank_short_name?: string;
}

const createBeneficiaryFormSchema = z.object({
  amount: z
    .string({ required_error: 'Please enter a valid amount' })
    .regex(/^\d+$/, { message: 'Please enter a valid amount' })
    .trim()
    .min(1, { message: 'Please enter a company size.' }),
  bank_name: z
    .string({ required_error: 'Please select a Bank name.' })
    .trim()
    .min(1, { message: 'Please select a bank name.' }),
  account_number: z
    .string({ required_error: 'Please enter account number.' })
    .regex(/^\d+$/, { message: 'account number must be a number.' })
    .trim()
    .min(1, { message: 'Please enter account number.' }),
});

export type createBeneficiaryFormValues = z.infer<
  typeof createBeneficiaryFormSchema
>;


interface SetUpAccountProps {
  isSetUpAccountOpen: boolean;
  setSetUpAccountState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  emailAddress: string;
}

const SetUpAccountSchema = z.object({
  amount: z
    .string({ required_error: 'Please enter a valid amount' })
    .regex(/^\d+$/, { message: 'Please enter a valid amount' })
    .trim()
    .min(1, { message: 'Please enter a company size.' }),
  bank_name: z
    .string().optional(),
  account_number: z
    .string({ required_error: 'Please enter account number.' })
    .regex(/^\d+$/, { message: 'account number must be a number.' })
    .trim()
    .min(1, { message: 'Please enter account number.' }),
});

export type SetUpAccountValues = z.infer<typeof SetUpAccountSchema>;

export function SetUpAccount({
  isSetUpAccountOpen,
  setSetUpAccountState,
  heading,
}: SetUpAccountProps) {



  const {
    state: isTransactionPinOpen,
    setState: setTransactionPinState,
    setTrue: openTransactionPin,
  } = useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();


  const {
    handleSubmit,
    register,
    control,
    setValue,
    formState: { errors },
  } = useForm<createBeneficiaryFormValues>({
    resolver: zodResolver(createBeneficiaryFormSchema),
    defaultValues: {
      amount: undefined,
      account_number: undefined,
      bank_name: undefined,
    },
  });

  const [selectedBank, setSelectedBank] = React.useState('');
  const [selectedBankCode, setSelectedBankCode] = React.useState('');
  const [bankNameGenerated, setBankNameGenerated] = React.useState('');
  const [withdrawAccountDetails, setWithdrawAccountDetails] = React.useState({
    bankName: '',
    bankCode: '',
    bankAccount: '',
    bankAccountName: '',
    withdrawAmount: 0,
  });

  const { data: user_bank_list } = useBanks()



  const bankData = user_bank_list?.bank;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  const getBankList = bankData?.map(
    (bank: { name: string; bank_code: string }) => ({
      name: bank.name,
      bankCode: bank.bank_code,
    })
  );

  const bankName = useWatch({ control, name: 'bank_name' });
  const accountNumber = useWatch({
    control,
    name: 'account_number',
  });

  const { mutate: fetchAccountName, isLoading: isAccountNameLoading } =
    useFetchAccountName();

  // fetch account name [Bank name, Bank code]
  React.useEffect(() => {
    if (bankName !== '' && accountNumber?.length >= 10) {
      const fetchData = {
        account_number: accountNumber,
        bank_code: selectedBankCode,
      };
      fetchAccountName(fetchData, {
        onSuccess: data => {
          setBankNameGenerated(data.data.data.account_name);
        },

        onError: error => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      });
    } else {
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bankName, accountNumber, fetchAccountName, selectedBankCode]);


  const onWithdrawalSubmit = (submittedData: createBeneficiaryFormValues) => {
    setWithdrawAccountDetails({
      bankAccount: submittedData.account_number,
      bankAccountName: bankNameGenerated,
      bankCode: selectedBankCode,
      bankName: submittedData.bank_name,
      withdrawAmount: Number(submittedData.amount)
    })
    openTransactionPin()
  };

  return (
    <>
      <Dialog open={isSetUpAccountOpen} onOpenChange={setSetUpAccountState}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
            <DialogClose className="ml-auto">Close</DialogClose>
          </DialogHeader>

          <DialogBody className="text-left bg-white">
            <p className='text-xs'>Withdrawal will be made to the account number entered
              below.</p>

            <form
              onSubmit={handleSubmit(onWithdrawalSubmit)}
            >
              <div className="mt-4 space-y-4 ">
                <div className="w-full">
                  <Controller
                    control={control}
                    name="bank_name"
                    render={({ field }) => (
                      <div className="flex w-full flex-col">

                        <Popover>
                          <PopoverTrigger asChild>
                            <div>
                              <Button
                                className={cn(
                                  'flex h-10 w-full justify-between truncate rounded-md bg-[#F5F7F9] px-2 py-2 font-sans text-[10px] uppercase text-black transition duration-300 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
                                  !field.value && 'text-muted-foreground'
                                )}
                                role="combobox"
                                type="button"
                              >
                                {field.value && getBankList
                                  ? getBankList.find(
                                    (bank: { name: string }) =>
                                      bank.name === field.value
                                  )?.name || selectedBank
                                  : 'Select bank'}
                                <svg
                                  className="ml-2 size-4 shrink-0 opacity-50"
                                  fill="none"
                                  height="24"
                                  viewBox="0 0 24 24"
                                  width="24"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M6.29297 10.707L7.70697 9.293L12 13.586L16.293 9.293L17.707 10.707L12 16.414L6.29297 10.707Z"
                                    fill="#4E4E4E"
                                  />
                                </svg>
                              </Button>
                            </div>
                          </PopoverTrigger>
                          <PopoverContent
                            align="end"
                            className="max-h-48 w-[300px] max-w-full overflow-auto bg-[#f1f1f29e] p-0 font-sans text-black md:basis-1/2"
                          >
                            <Command>


                              <CommandInput placeholder="Search bank..." />
                              <CommandEmpty>Bank name not found.</CommandEmpty>
                              <CommandGroup>

                                {getBankList?.map(
                                  (bank: {
                                    name: string;
                                    bankCode: string;
                                  }) => (
                                    <CommandItem
                                      key={bank.name}
                                      value={bank.name}
                                      onSelect={(value: React.SetStateAction<string>) => {
                                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                        //@ts-ignore
                                        setValue('bank_name', value);
                                        field.onChange(value);
                                        setSelectedBank(value);
                                        setSelectedBankCode(bank.bankCode);
                                      }}
                                    >
                                      <svg
                                        className={cn(
                                          'mr-2 h-4 w-4',
                                          bank.name === field.value
                                            ? 'opacity-100'
                                            : 'opacity-0'
                                        )}
                                        fill="#000000"
                                        height="32"
                                        viewBox="0 0 256 256"
                                        width="32"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <path d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"></path>
                                      </svg>
                                      {bank.name}
                                    </CommandItem>
                                  )
                                )}
                              </CommandGroup>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>
                    )}
                  />
                </div>

                <div className="w-full">
                  {/* <Label
                  className="block text-xs leading-[27px] text-label-text"
                  htmlFor="account_number"
                >
                  Account number
                </Label> */}

                  <Input
                    autoCapitalize="none"
                    autoComplete="off"
                    autoCorrect="off"
                    className="bg-[#F5F7F9]"
                    id="account_number"
                    placeholder="Account number"
                    type="text"
                    {...register('account_number')}
                  />

                  {errors?.account_number && (
                    <FormError errorMessage={errors.account_number.message} />
                  )}
                </div>

                {isAccountNameLoading ? (
                  <SmallSpinner className="mr-2 animate-spin" color="#032282" />
                ) : (
                  <div className="mt-4 rounded-md bg-[#F5F7F9] px-3 py-2">
                    <p className="block font-heading text-xs leading-[27px]">
                      {bankNameGenerated || '--'}
                    </p>
                  </div>
                )}

                <div className="w-full">

                  <Input
                    autoCapitalize="none"
                    autoComplete="off"
                    autoCorrect="off"
                    className="bg-[#F5F7F9]"
                    id="amount"
                    placeholder="Amount"
                    type="text"
                    {...register('amount')}
                  />

                  {errors?.amount && (
                    <FormError errorMessage={errors.amount.message} />
                  )}
                </div>

              </div>


              <Button
                className="mt-9 w-full whitespace-nowrap rounded-[8px]
         border-[0.3px] px-4 py-[10px] text-sm font-medium text-white bg-[#032282]"
                type="submit"
              >
                <span className="inline-block">Withdraw</span>
              </Button>
            </form>
          </DialogBody>
        </DialogContent>

      </Dialog >

      <EnterWithdrawalDetailsPinModal
        heading={'Transaction'}
        isPinModalOpen={isTransactionPinOpen}
        setPinModalState={setTransactionPinState}
        withdrawalDetails={withdrawAccountDetails}
      />


      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
