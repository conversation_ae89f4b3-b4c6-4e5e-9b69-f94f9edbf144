'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
    Input,
} from '@/components/core';
import { useUser } from '@/app/(auth)/(onboarding)/misc';


interface ProfileSettingsProps {
}

const WalletFormSchema = z.object({
    plan_name: z
        .string({ required_error: 'Please enter a plan name' })
        .trim()
        .min(1, { message: 'Please enter a plan name' }),
});

export type WalletFormValues = z.infer<typeof WalletFormSchema>;

export function ProfileSettings({ }: ProfileSettingsProps) {
    const {
        data: viewUserData } = useUser();

    const { first_name, last_name, email, phone_number, gender, state, lga, nearest_landmark, street } = viewUserData?.user_data || {};

    const {
        formState: { },
    } = useForm<WalletFormValues>({
        resolver: zodResolver(WalletFormSchema),
        defaultValues: {
            plan_name: undefined,
        },
    });


    return (
        <>
            <div className="w-full relative">
                <form
                    className=" relative w-full mt-3 space-y-3"
                >
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Full name</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${first_name + " " + last_name || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Email</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${email || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Phone number</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${phone_number || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Gender</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${gender || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>State</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${state || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>L.G.A</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${lga || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Nearest landmark</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${nearest_landmark || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Street</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            defaultValue={`${street || ""}`}
                            type="text"
                            readOnly
                        />
                    </div>
                </form>
            </div>
        </>
    );
}
