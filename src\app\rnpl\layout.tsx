'use client'
import Image from 'next/image';
import * as React from 'react';

import { cn } from '@/utils/classNames';
// import { Slideshow } from '../(auth)/(onboarding)/misc';
import { SlideshowRnpl } from '../(auth)/(onboarding)/misc/components/SlideshowRnpl';
import Link from 'next/link';
import SeedsPenniesLogo from '@/icons/SeedsPenniesLogo';
// import SeedsPennies from "@/icons/SeedsPenniesLogo";
// import Link from 'next/link';


export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <React.Suspense>
      <div className="h-screen-small justify-between overflow-auto bg-[#000D36] md:flex md:flex-row-reverse md:justify-normal md:pb-0">
        <div className="relative md:flex md:basis-1/2 md:flex-col md:justify-center md:overflow-y-auto md:py-8 md:[@media(min-height:520px)]:items-center">
          <div
            className="absolute inset-y-0 left-[-10%] right-0 md:fixed md:left-auto md:w-[50vw]"
            aria-hidden
          >
            <Image
              alt="Woman smiling whilst operating a POS device"
              blurDataURL="eDI4z}~TIW_49a]yyD=_Ip%N00nh%MogxtI[^iS$of9ajDxaNexCoz"
              className="w-full object-cover"
              placeholder="blur"
              sizes="100vw"
              src="/images/login/login-rnpl.jpg"
              fill
            />

            <div
              className={cn(
                'absolute inset-0 -scale-x-100 from-black/80 from-[50.08%] to-[#032282]/80 to-[104.44%] bg-gradient-225'
              )}
            />
          </div>


          <header className="relative mx-auto flex w-full max-w-[32.375rem] items-center justify-between gap-4 px-6 py-[3vh] bg-[#000D36] h-[92px] md:hidden">


            {/* 
          <LinkButton className="" href="/" size="unstyled" variant="unstyled">
            <span className="sr-only">Go home</span>

            <svg
              fill="none"
              height={34}
              viewBox="0 0 38 34"
              width={38}
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                fill="url(#a)"
                height={34}
                opacity={0.2}
                rx={7}
                width={38}
              />
              <path
                d="M26.358 13.675 20.9 9.308c-1.067-.85-2.733-.858-3.792-.008l-5.458 4.375c-.783.625-1.258 1.875-1.092 2.858l1.05 6.284c.242 1.408 1.55 2.516 2.975 2.516h8.834c1.408 0 2.741-1.133 2.983-2.525l1.05-6.283c.15-.975-.325-2.225-1.092-2.85Z"
                fill="#fff"
              />
              <path
                d="M19 22.625a.63.63 0 0 1-.625-.625v-2.5a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625V22a.63.63 0 0 1-.625.625Z"
                fill="#000"
              />
              <defs>
                <linearGradient
                  gradientUnits="userSpaceOnUse"
                  id="a"
                  x1={3}
                  x2={48.5}
                  y1={-28}
                  y2={69}
                >
                  <stop stopColor="#fff" />
                  <stop offset={1} stopColor="#fff" stopOpacity={0} />
                </linearGradient>
              </defs>
            </svg>
          </LinkButton> */}
            <Link className={cn(" font-semibold text-xl lg:text-2xl relative z-50 block md:hidden")} href="/">
              <SeedsPenniesLogo />
            </Link>


          </header>

          {children}
        </div>

        <SlideshowRnpl />

      </div>
    </React.Suspense>
  );
}
