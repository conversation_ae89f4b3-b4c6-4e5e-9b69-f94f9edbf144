'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
import type { AxiosError } from 'axios';
// import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/core/Button';
import { ErrorModal } from '@/components/core/ErrorModal';
import { FormError } from '@/components/core/FormError';
import { Input } from '@/components/core/Input';
import { LoaderModal } from '@/components/core/LoaderModal';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';

import { useCreatePasscode } from '../api';

const CreatePasscodeFormSchema = z.object({
  pin1: z
    .string({ required_error: 'Please enter a pin.' })
    .trim()
    .min(6, { message: 'Your pin must be 6 numeric characters.' })
    .max(6, { message: 'Your pin must be 6 numeric characters.' }),
  pin2: z
    .string({ required_error: 'Please confirm your pin.' })
    .trim()
    .min(6, { message: 'Your pin must be 6 numeric characters.' })
    .max(6, { message: 'Your pin must be 6 numeric characters.' }),
});

export type CreatePasscodeFormValues = z.infer<typeof CreatePasscodeFormSchema>;

interface CreatePasscodeFormProps {
  email: string;
  phone: string;
}

export function CreatePasscodeForm({ phone, email }: CreatePasscodeFormProps) {
  const router = useRouter();
  const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
    useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<CreatePasscodeFormValues>({
    resolver: zodResolver(CreatePasscodeFormSchema),
  });

  const { mutate: createPasscode, isLoading: isCreatePasscodeLoading } =
    useCreatePasscode();

  const onCreatePasscodeSubmit = async (
    submittedData: CreatePasscodeFormValues
  ) => {
    createPasscode(
      { ...submittedData, phone_number: phone },
      {
        onSuccess: async () => {
          openLoaderModal();

          // await signIn('credentials', {
          //   email,
          //   password: submittedData.pin1,
          //   redirect: false,
          // });

          // router.push(`/sign-up/security-questions/?phone=${phone}`);
          router.push(
            `/sign-up/bvn/?email=${email}&phone=${phone}`
          );
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );

    // router.push(`/sign-up/security-questions/?phone=${phone}`);
  };

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen} />

      <form
        className="relative"
        onSubmit={handleSubmit(onCreatePasscodeSubmit)}
      >
        <Label className="sr-only" htmlFor="pin1">
          Enter passcode
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="pin1"
          placeholder="Enter passcode"
          type="text"
          {...register('pin1')}
        />
        {errors?.pin1 && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.pin1.message}
          />
        )}

        <Label className="sr-only" htmlFor="confirm-pin">
          Confirm passcode
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="confirm-pin"
          placeholder="Confirm passcode"
          type="text"
          {...register('pin2')}
        />
        {errors?.pin2 && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.pin2.message}
          />
        )}

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isCreatePasscodeLoading}
          type="submit"
          variant="white"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isCreatePasscodeLoading ? 'Loading' : 'Continue'}</span>
            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
      </form>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
