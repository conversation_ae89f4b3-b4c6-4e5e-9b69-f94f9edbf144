import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg
        fill="none"
        height={24}
        viewBox="0 0 24 24"
        width={24}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M19.3 7.91998V13.07C19.3 16.15 17.54 17.47 14.9 17.47H6.10995C5.65995 17.47 5.22996 17.43 4.82996 17.34C4.57996 17.3 4.33996 17.23 4.11996 17.15C2.61996 16.59 1.70996 15.29 1.70996 13.07V7.91998C1.70996 4.83998 3.46995 3.52002 6.10995 3.52002H14.9C17.14 3.52002 18.75 4.47001 19.18 6.64001C19.25 7.04001 19.3 7.44998 19.3 7.91998Z"
            fill="#1255C3"
            opacity={0.4}
        />
        <path
            d="M22.3001 10.92V16.07C22.3001 19.15 20.5401 20.47 17.9001 20.47H9.11008C8.37008 20.47 7.70009 20.37 7.12009 20.15C5.93009 19.71 5.12008 18.8 4.83008 17.34C5.23008 17.43 5.66008 17.47 6.11008 17.47H14.9001C17.5401 17.47 19.3001 16.15 19.3001 13.07V7.91998C19.3001 7.44998 19.2601 7.03001 19.1801 6.64001C21.0801 7.04001 22.3001 8.37998 22.3001 10.92Z"
            fill="#1255C3"
        />
        <path
            d="M10.4999 13.14C11.9579 13.14 13.1399 11.958 13.1399 10.5C13.1399 9.04197 11.9579 7.85999 10.4999 7.85999C9.04185 7.85999 7.85986 9.04197 7.85986 10.5C7.85986 11.958 9.04185 13.14 10.4999 13.14Z"
            fill="#1255C3"
        />
        <path
            d="M4.77979 8.25C4.36979 8.25 4.02979 8.59 4.02979 9V12C4.02979 12.41 4.36979 12.75 4.77979 12.75C5.18979 12.75 5.52979 12.41 5.52979 12V9C5.52979 8.59 5.19979 8.25 4.77979 8.25Z"
            fill="#1255C3"
        />
        <path
            d="M16.21 8.25C15.8 8.25 15.46 8.59 15.46 9V12C15.46 12.41 15.8 12.75 16.21 12.75C16.62 12.75 16.96 12.41 16.96 12V9C16.96 8.59 16.63 8.25 16.21 8.25Z"
            fill="#1255C3"
        />
    </svg>
);
export default SVGComponent;
