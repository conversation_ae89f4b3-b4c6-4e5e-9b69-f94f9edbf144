'use client'

import { useSearchParams } from 'next/navigation'
import * as React from 'react';

import { OnboardingPageWrapper } from '../../misc/components';
import { GetStartedForm } from './misc/components';
import { useOnboardingPlan } from '@/store/onBoardingPlan';

export default function GetStarted() {

  const searchParams = useSearchParams();
  const referral_code = searchParams.get("referral_code");

  const { setReferallCodeOnboading, referall_code_onboarding } = useOnboardingPlan();

  React.useEffect(() => {
    if (referral_code === null) {

    } else {
      setReferallCodeOnboading(referral_code)
    }
  }, [referral_code, referall_code_onboarding, setReferallCodeOnboading])



  return (
    <React.Suspense>
      <div className='bg-[#000D36] relative p-6 block md:hidden'>
        <h1 className='font-heading font-semibold text-xl tracking-[2%] text-white'>Onlending enrollment </h1>
        <p className='text-[#FFFFFFB2] text-sm leading-[19px] font-normal font-sans mt-2'>
          In order to know you more and determine your
          lending capabilities please complete the details
          below to start lending
        </p>
      </div>

      <OnboardingPageWrapper
        heading="Onlending enrollment "
        subHeading=" In order to know you more and determine your
          lending capabilities please complete the details
          below to start lending"
      >
        <GetStartedForm
          referral_code={referall_code_onboarding}
        />
      </OnboardingPageWrapper>
    </React.Suspense>
  );
}
