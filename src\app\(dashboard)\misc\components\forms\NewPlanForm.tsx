'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
    Button,
    FormError,
    Input,
} from '@/components/core';

// import { addCommasToNumber } from '@/utils/numbers';
import { useRouter } from 'next/navigation';
import { useOnboardingPlan } from '@/store/onBoardingPlan';


interface NewPlanFormProps {
}

const WalletFormSchema = z.object({
    plan_name: z
        .string({ required_error: 'Please enter a plan name' })
        .trim()
        .min(1, { message: 'Please enter a plan name' }),
});

export type WalletFormValues = z.infer<typeof WalletFormSchema>;

export function NewPlanForm({ }: NewPlanFormProps) {

    const router = useRouter()

    const { setPlanName } = useOnboardingPlan();

    const {
        handleSubmit,
        register,
        formState: { errors },
    } = useForm<WalletFormValues>({
        resolver: zodResolver(WalletFormSchema),
        defaultValues: {
            plan_name: undefined,
        },
    });

    const onWithdrawalSubmit = (submittedData: WalletFormValues) => {
        setPlanName(submittedData.plan_name)
        router.push('/lending-options/lending-duration')
    };

    return (
        <>
            <div className="w-full relative">
                <p className="text-xl text-[#242424] font-semibold">
                    New onlending plan
                </p>
                <p className='text-xs text-[#4E4E4E]'>
                    Lend money to borrowers and earn with easy with few easy steps.
                </p>
                <form
                    className="mt-[30px] relative w-full"
                    onSubmit={handleSubmit(onWithdrawalSubmit)}
                >
                    <div>
                        <p className='text-xs text-[#4E4E4E] mb-[6px]'>Loan plan name</p>
                        <Input
                            autoCapitalize="none"
                            autoComplete="off"
                            autoCorrect="off"
                            id="amount"
                            placeholder="Enter name"
                            type="text"
                            {...register('plan_name')}
                        />

                        {errors?.plan_name && (
                            <FormError errorMessage={errors.plan_name.message} />
                        )}

                        <p className='text-xs text-[#4E4E4E] mt-[6px]'>E.g. New car, General savings.</p>
                    </div>

                    <div className='mt-52'>
                        <Button
                            className="font-sora w-full rounded-lg bg-[#032282] px-4 py-[10px] text-right text-sm font-normal text-white"
                        // onClick={openSavedBenneficiariesModal}
                        >
                            Proceed
                        </Button>
                    </div>
                </form>
            </div>
        </>
    );
}
