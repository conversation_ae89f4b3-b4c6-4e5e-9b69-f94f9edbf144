'use client';

import { useAllPlansId } from '@/app/(dashboard)/misc/api/getAllPlanId';
import {
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  Button,
} from '@/components/core';
import { useOnboardingPlan } from '@/store/onBoardingPlan';

interface SuccessModalWithdrawalProps {
  isSuccessModalWithdrawalOpen: boolean;
  setSuccessModalWithdrawalState: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  closeButtonReplacement?: React.ReactNode;
  children?: React.ReactNode;
}

export function SuccessModalWithdrawal({
  isSuccessModalWithdrawalOpen,
  setSuccessModalWithdrawalState,
  closeButtonReplacement,
}: SuccessModalWithdrawalProps) {


  const { emailAddress, createPlanResponse } = useOnboardingPlan();


  const { data: _specificPlan } = useAllPlansId(emailAddress as string, Number(createPlanResponse?.id))

  // console.log(specificPlan, "lksdfkgj")
  return (
    <Dialog open={isSuccessModalWithdrawalOpen} onOpenChange={setSuccessModalWithdrawalState}>
      <DialogContent className='bg-[#F5F7F9]'>
        <DialogHeader>
          {closeButtonReplacement || (
            <DialogClose className="ml-auto">Close</DialogClose>
          )}
        </DialogHeader>

        <DialogBody className="p-6 text-center">
          <div className="p-4 w-full rounded-xl bg-white flex flex-col items-center justify-center">
            <svg fill="none" height="66" viewBox="0 0 66 66" width="66" xmlns="http://www.w3.org/2000/svg">
              <rect fill="#ECFDF5" height="66" rx="33" width="66" />
              <path d="M33.0002 52.198C43.6031 52.198 52.1984 43.6027 52.1984 32.9999C52.1984 22.397 43.6031 13.8018 33.0002 13.8018C22.3974 13.8018 13.8021 22.397 13.8021 32.9999C13.8021 43.6027 22.3974 52.198 33.0002 52.198Z" fill="#17C373" />
              <path d="M30.2741 39.8726C29.8902 39.8726 29.5254 39.719 29.2566 39.4502L23.8235 34.0171C23.2668 33.4604 23.2668 32.5389 23.8235 31.9821C24.3803 31.4254 25.3018 31.4254 25.8585 31.9821L30.2741 36.3977L40.1419 26.5299C40.6987 25.9731 41.6202 25.9731 42.1769 26.5299C42.7337 27.0866 42.7337 28.0081 42.1769 28.5649L31.2916 39.4502C31.0228 39.719 30.6581 39.8726 30.2741 39.8726Z" fill="white" />
            </svg>
            <p className='text-[#14151B] text-base font-semibold mt-2'>Successful !</p>
            <p className='text-xs text-[#556575] '>Your withdrawal was successful</p>
          </div>


          <Button className='py-4 mt-6' size="fullWidth" variant="default"
            onClick={() => {
              location.reload()
            }}>
            Okay
          </Button>

        </DialogBody>
      </DialogContent>
    </Dialog>
  );
}
