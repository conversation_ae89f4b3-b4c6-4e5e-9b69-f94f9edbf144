import * as React from 'react';
import { usePathname } from 'next/navigation';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/core';
import { cn } from '@/utils/classNames';
import { clsx } from 'clsx';


export function ButtonNavigation() {
    const pathname = usePathname();

    return (
        <main className={clsx(
            'fixed bottom-0 z-40 mx-auto max-w-[34.375rem] flex w-full flex-row items-center justify-between bg-white'
        )}
        // className='fixed bottom-0 z-40 flex size-full flex-row items-center justify-between bg-white px-6  pb-3 mx-auto max-w-full left-1/2 -translate-x-1/2'
        >
            <div className='w-full flex items-center justify-center'>
                <Tabs className="w-full" defaultValue={pathname}>
                    <TabsList className="grid w-full grid-cols-3 py-[10px] px-6 bg-white drop-shadow-md">
                        <TabsTrigger
                            className={`rounded-[20px]
                    bg-white w-auto text-[13px] font-medium
                     px-5 text-[#A5B3CD]  data-[state=active]:bg-[#F1F8FF] data-[state=active]:font-bold data-[state=active]:text-[#032282] data-[state=active]:shadow-sm`}
                            value="/"
                        >

                            <LinkButton href='/' variant={"unstyled"}>
                                <div className='flex gap-[5px] items-center '>
                                    <svg className={cn(
                                        pathname === '/'
                                            ? 'block'
                                            : 'hidden',)}
                                        fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M17.3584 6.67485L11.9 2.30819C10.8334 1.45819 9.16672 1.44985 8.10838 2.29985L2.65005 6.67485C1.86672 7.29985 1.39172 8.54985 1.55838 9.53319L2.60838 15.8165C2.85005 17.2249 4.15838 18.3332 5.58338 18.3332H14.4167C15.825 18.3332 17.1584 17.1999 17.4 15.8082L18.45 9.52485C18.6 8.54985 18.125 7.29985 17.3584 6.67485Z" fill="#032282" />
                                        <path d="M10 15.625C9.65833 15.625 9.375 15.3417 9.375 15V12.5C9.375 12.1583 9.65833 11.875 10 11.875C10.3417 11.875 10.625 12.1583 10.625 12.5V15C10.625 15.3417 10.3417 15.625 10 15.625Z" fill="white" />
                                    </svg>
                                    <p>Home</p>
                                </div>
                            </LinkButton>
                        </TabsTrigger>

                        <TabsTrigger
                            className={`rounded-[20px]
                    bg-white w-auto text-[13px] font-medium
                     px-5 text-[#A5B3CD]  data-[state=active]:bg-[#F1F8FF] data-[state=active]:font-bold data-[state=active]:text-[#032282] data-[state=active]:shadow-sm`}
                            value="/transactions">


                            <LinkButton href='/transactions' variant={"unstyled"}>
                                <div className='flex gap-[5px] items-center '>
                                    <svg className={cn(
                                        pathname === '/transactions'
                                            ? 'block'
                                            : 'hidden',)}
                                        fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.0475 14.775C5.6625 14.115 6.6 14.1675 7.14 14.8875L7.8975 15.9C8.505 16.7025 9.4875 16.7025 10.095 15.9L10.8525 14.8875C11.3925 14.1675 12.33 14.115 12.945 14.775C14.28 16.2 15.3675 15.7275 15.3675 13.7325V5.28C15.375 2.2575 14.67 1.5 11.835 1.5H6.165C3.33 1.5 2.625 2.2575 2.625 5.28V13.725C2.625 15.7275 3.72 16.1925 5.0475 14.775Z" fill="#032282" />
                                        <path d="M12 5.8125H6C5.6925 5.8125 5.4375 5.5575 5.4375 5.25C5.4375 4.9425 5.6925 4.6875 6 4.6875H12C12.3075 4.6875 12.5625 4.9425 12.5625 5.25C12.5625 5.5575 12.3075 5.8125 12 5.8125Z" fill="white" />
                                        <path d="M11.25 8.8125H6.75C6.4425 8.8125 6.1875 8.5575 6.1875 8.25C6.1875 7.9425 6.4425 7.6875 6.75 7.6875H11.25C11.5575 7.6875 11.8125 7.9425 11.8125 8.25C11.8125 8.5575 11.5575 8.8125 11.25 8.8125Z" fill="white" />
                                    </svg>

                                    <p>Transactions</p>
                                </div>
                            </LinkButton>

                        </TabsTrigger>

                        <TabsTrigger
                            className={`rounded-[20px]
                    bg-white w-auto text-[13px] font-medium
                     px-5 text-[#A5B3CD]  data-[state=active]:bg-[#F1F8FF] data-[state=active]:font-bold data-[state=active]:text-[#032282] data-[state=active]:shadow-sm`}
                            value="/profile">


                            <LinkButton href='/profile' variant={"unstyled"}>
                                <div className='flex gap-[5px] items-center '>


                                    <svg className={cn(
                                        pathname === '/profile'
                                            ? 'block'
                                            : 'hidden',)}
                                        fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.1329 9.68366C10.1079 9.68366 10.0912 9.68366 10.0662 9.68366C10.0246 9.67533 9.96623 9.67533 9.91623 9.68366C7.49956 9.60866 5.67456 7.70866 5.67456 5.36699C5.67456 2.98366 7.61623 1.04199 9.99956 1.04199C12.3829 1.04199 14.3246 2.98366 14.3246 5.36699C14.3162 7.70866 12.4829 9.60866 10.1579 9.68366C10.1496 9.68366 10.1412 9.68366 10.1329 9.68366ZM9.99956 2.29199C8.30789 2.29199 6.92456 3.67533 6.92456 5.36699C6.92456 7.03366 8.22456 8.37533 9.88289 8.43366C9.92456 8.42533 10.0412 8.42533 10.1496 8.43366C11.7829 8.35866 13.0662 7.01699 13.0746 5.36699C13.0746 3.67533 11.6912 2.29199 9.99956 2.29199Z" fill="#032282" />
                                        <path d="M10.1413 18.7913C8.50801 18.7913 6.86634 18.3747 5.62467 17.5413C4.46634 16.7747 3.83301 15.7247 3.83301 14.583C3.83301 13.4413 4.46634 12.383 5.62467 11.608C8.12467 9.94967 12.1747 9.94967 14.658 11.608C15.808 12.3747 16.4497 13.4247 16.4497 14.5663C16.4497 15.708 15.8163 16.7663 14.658 17.5413C13.408 18.3747 11.7747 18.7913 10.1413 18.7913ZM6.31634 12.658C5.51634 13.1913 5.08301 13.8747 5.08301 14.5913C5.08301 15.2997 5.52467 15.983 6.31634 16.508C8.39134 17.8997 11.8913 17.8997 13.9663 16.508C14.7663 15.9747 15.1997 15.2913 15.1997 14.5747C15.1997 13.8663 14.758 13.183 13.9663 12.658C11.8913 11.2747 8.39134 11.2747 6.31634 12.658Z" fill="#032282" />
                                    </svg>


                                    <p>Profile</p>
                                </div>
                            </LinkButton>

                        </TabsTrigger>
                    </TabsList>
                </Tabs>
            </div>
        </main>
    );
}
